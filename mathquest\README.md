# MathQuest - Gamified Mathematics Learning Platform

A comprehensive, interactive mathematics learning web application designed for students in grades 1-10. MathQuest transforms traditional math education through gamification, personalized learning paths, and engaging interactive content.

## 🌟 Features

### 🎮 Gamified Learning Experience
- **Interactive Games**: Engaging math games tailored to each grade level
- **Achievement System**: Badges, points, and rewards for progress milestones
- **Progress Tracking**: Visual progress indicators and performance analytics
- **Leaderboards**: Friendly competition with classmates and friends

### 📚 Comprehensive Curriculum
- **40-Week Structure**: Complete academic year coverage for each grade (1-10)
- **Standards-Aligned**: Following international mathematics education standards
- **Progressive Learning**: Carefully structured difficulty progression
- **Multiple Learning Styles**: Visual, auditory, and kinesthetic learning approaches

### 👥 Multi-User Support
- **Student Accounts**: Personalized learning dashboards
- **Teacher Dashboards**: Class management and progress monitoring
- **Parent Access**: Home progress tracking and reports
- **Admin Panel**: Content management and system administration

### 🔒 Safety & Privacy
- **COPPA Compliant**: Child-safe data handling
- **GDPR Compliant**: European privacy standards
- **Secure Authentication**: Protected user accounts
- **Content Moderation**: Safe learning environment

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- MongoDB 7.0+
- Docker (optional, for containerized deployment)

### Local Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd mathquest
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

4. **Start MongoDB**
   ```bash
   # Using Docker
   docker run -d -p 27017:27017 --name mongodb mongo:7.0

   # Or use your local MongoDB installation
   mongod
   ```

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Docker Deployment

1. **Using Docker Compose (Recommended)**
   ```bash
   docker-compose up -d
   ```

2. **Manual Docker Build**
   ```bash
   docker build -t mathquest .
   docker run -p 3000:3000 mathquest
   ```

## 📖 Curriculum Overview

### Grade 1: Foundation Building
- **Week 1-4**: Numbers 1-20, Basic Shapes
- **Week 5-8**: Addition & Subtraction to 10
- **Week 9-12**: Measurement & Comparison
- **Week 13-16**: Time & Money Basics
- **Week 17-20**: Patterns & Sorting
- **Week 21-24**: 2D & 3D Shapes
- **Week 25-28**: Addition & Subtraction to 20
- **Week 29-32**: Data & Graphs
- **Week 33-36**: Fractions Introduction
- **Week 37-40**: Review & Assessment

### Grade 2: Building Fluency
- **Week 1-4**: Place Value to 100
- **Week 5-8**: Addition with Regrouping
- **Week 9-12**: Subtraction with Regrouping
- **Week 13-16**: Measurement Units
- **Week 17-20**: Time & Calendar
- **Week 21-24**: Money & Making Change
- **Week 25-28**: Geometry & Shapes
- **Week 29-32**: Fractions & Parts of Whole
- **Week 33-36**: Data Collection & Analysis
- **Week 37-40**: Problem Solving & Review

*[Curriculum continues for Grades 3-10 with increasing complexity]*

## 🎯 Learning Outcomes by Grade

### Grade 1 Learning Objectives
- Count, read, and write numbers 1-100
- Understand place value (tens and ones)
- Add and subtract within 20
- Identify and describe 2D and 3D shapes
- Measure length using non-standard units
- Tell time to the hour and half-hour
- Recognize and extend patterns

### Grade 2 Learning Objectives
- Understand place value to 1000
- Add and subtract 2-digit numbers with regrouping
- Solve word problems using addition and subtraction
- Measure using standard units (inches, feet, centimeters)
- Work with money (counting coins, making change)
- Understand fractions as parts of a whole
- Create and interpret simple graphs

*[Learning outcomes continue for all grades]*

## 🎮 Game Types & Activities

### Interactive Game Categories

1. **Number Games**
   - Counting objects
   - Number recognition
   - Place value builders
   - Number line jumps

2. **Operation Games**
   - Addition/subtraction races
   - Multiplication tables
   - Division challenges
   - Mental math speedruns

3. **Geometry Games**
   - Shape matching
   - Pattern completion
   - Spatial reasoning puzzles
   - Coordinate plane adventures

4. **Problem Solving Games**
   - Word problem scenarios
   - Logic puzzles
   - Real-world applications
   - Multi-step challenges

## 🏗️ Technical Architecture

### Frontend Stack
- **Next.js 15**: React framework with App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first styling
- **Framer Motion**: Smooth animations
- **Lucide React**: Beautiful icons

### Backend Stack
- **Node.js**: Server runtime
- **MongoDB**: Document database
- **Mongoose**: MongoDB object modeling
- **NextAuth.js**: Authentication system
- **bcryptjs**: Password hashing

### Development Tools
- **ESLint**: Code linting
- **Prettier**: Code formatting
- **Docker**: Containerization
- **TypeScript**: Static type checking

## 📊 Database Schema

### User Model
```typescript
interface IUser {
  name: string;
  email: string;
  password: string;
  role: 'student' | 'teacher' | 'parent' | 'admin';
  grade?: number;
  dateOfBirth?: Date;
  avatar?: string;
}
```

### Progress Model
```typescript
interface IProgress {
  userId: ObjectId;
  grade: number;
  week: number;
  topicId: string;
  completed: boolean;
  score: number;
  timeSpent: number;
  attempts: number;
  achievements: string[];
}
```

### Achievement Model
```typescript
interface IAchievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'completion' | 'streak' | 'score' | 'time' | 'special';
  points: number;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}
```

## 🧪 Testing Strategy

### Testing Approach
- **Unit Tests**: Component and utility function testing
- **Integration Tests**: API endpoint and database testing
- **E2E Tests**: Full user journey testing
- **Performance Tests**: Load and stress testing

### Recommended Testing Tools
- **Jest**: Unit testing framework
- **React Testing Library**: Component testing
- **Cypress**: End-to-end testing
- **Playwright**: Cross-browser testing

## 🚀 Deployment Options

### Production Deployment

1. **Vercel (Recommended)**
   ```bash
   npm run build
   vercel deploy
   ```

2. **Docker Production**
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

3. **Traditional Hosting**
   ```bash
   npm run build
   npm start
   ```

### Environment Configuration

```bash
# Production Environment Variables
NODE_ENV=production
MONGODB_URI=mongodb://your-production-db
NEXTAUTH_SECRET=your-production-secret
NEXTAUTH_URL=https://your-domain.com
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [Wiki](https://github.com/your-repo/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-repo/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-repo/discussions)
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- Mathematics curriculum standards from Common Core and international sources
- Educational research from leading mathematics education institutions
- Open source community for excellent tools and libraries
- Teachers and students who provided valuable feedback

---

**Made with ❤️ for mathematics education**
