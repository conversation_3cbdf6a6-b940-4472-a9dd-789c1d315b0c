version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: mathquest-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: mathquest
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - mathquest-network

  # Next.js Application
  mathquest-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mathquest-app
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - MONGODB_URI=********************************************************************
      - NEXTAUTH_SECRET=your-secret-key-here
      - NEXTAUTH_URL=http://localhost:3000
    ports:
      - "3000:3000"
    depends_on:
      - mongodb
    networks:
      - mathquest-network

volumes:
  mongodb_data:

networks:
  mathquest-network:
    driver: bridge
