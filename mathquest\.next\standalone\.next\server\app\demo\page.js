(()=>{var t={};t.id=436,t.ids=[436],t.modules={365:(t,e,i)=>{Promise.resolve().then(i.bind(i,3920))},440:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>s});var r=i(1658);let s=async t=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await t.params,"favicon.ico")+""}]},846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},2318:(t,e,i)=>{Promise.resolve().then(i.bind(i,7552))},2582:(t,e,i)=>{Promise.resolve().then(i.bind(i,3622))},3033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3622:(t,e,i)=>{"use strict";i.d(e,{Providers:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\components\\Providers.tsx","Providers")},3873:t=>{"use strict";t.exports=require("path")},3920:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\augument\\\\onstud\\\\mathquest\\\\src\\\\app\\\\demo\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\demo\\page.tsx","default")},4431:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>u,metadata:()=>a,viewport:()=>l});var r=i(7413),s=i(5041),n=i.n(s);i(1135);var o=i(3622);let a={title:"MathQuest - Gamified Mathematics Learning",description:"Interactive mathematics learning platform for grades 1-10 with games, achievements, and personalized progress tracking",keywords:"mathematics, learning, education, games, kids, math, interactive",authors:[{name:"MathQuest Team"}]},l={width:"device-width",initialScale:1};function u({children:t}){return(0,r.jsx)("html",{lang:"en",className:"h-full",children:(0,r.jsx)("body",{className:`${n().className} h-full bg-gradient-to-br from-blue-50 to-purple-50 antialiased`,children:(0,r.jsx)(o.Providers,{children:t})})})}},4761:(t,e,i)=>{Promise.resolve().then(i.t.bind(i,6346,23)),Promise.resolve().then(i.t.bind(i,5543,23)),Promise.resolve().then(i.t.bind(i,5656,23)),Promise.resolve().then(i.t.bind(i,99,23)),Promise.resolve().then(i.t.bind(i,8243,23)),Promise.resolve().then(i.t.bind(i,8827,23)),Promise.resolve().then(i.t.bind(i,2763,23)),Promise.resolve().then(i.t.bind(i,7173,23))},6544:(t,e,i)=>{"use strict";let r;i.r(e),i.d(e,{default:()=>oK});var s,n,o=i(687),a=i(3210);function l(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function u(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function h(t,e,i,r){if("function"==typeof e){let[s,n]=u(r);e=e(void 0!==i?i:t.custom,s,n)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[s,n]=u(r);e=e(void 0!==i?i:t.custom,s,n)}return e}function d(t,e,i){let r=t.getProps();return h(r,e,void 0!==i?i:r.custom,t)}function c(t,e){return t?.[e]??t?.default??t}let m=t=>t,p={},f=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],g={value:null,addProjectionMetrics:null};function y(t,e){let i=!1,r=!0,s={delta:0,timestamp:0,isProcessing:!1},n=()=>i=!0,o=f.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,r=new Set,s=!1,n=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,n=!1)=>{let a=n&&s?i:r;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{r.delete(t),o.delete(t)},process:t=>{if(a=t,s){n=!0;return}s=!0,[i,r]=[r,i],i.forEach(u),e&&g.value&&g.value.frameloop[e].push(l),l=0,i.clear(),s=!1,n&&(n=!1,h.process(t))}};return h}(n,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:h,update:d,preRender:c,render:m,postRender:y}=o,v=()=>{let n=p.useManualTiming?s.timestamp:performance.now();i=!1,p.useManualTiming||(s.delta=r?1e3/60:Math.max(Math.min(n-s.timestamp,40),1)),s.timestamp=n,s.isProcessing=!0,a.process(s),l.process(s),u.process(s),h.process(s),d.process(s),c.process(s),m.process(s),y.process(s),s.isProcessing=!1,i&&e&&(r=!1,t(v))},x=()=>{i=!0,r=!0,s.isProcessing||t(v)};return{schedule:f.reduce((t,e)=>{let r=o[e];return t[e]=(t,e=!1,s=!1)=>(i||x(),r.schedule(t,e,s)),t},{}),cancel:t=>{for(let e=0;e<f.length;e++)o[f[e]].cancel(t)},state:s,steps:o}}let{schedule:v,cancel:x,state:b,steps:w}=y("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:m,!0),k=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],P=new Set(k),T=new Set(["width","height","top","left","right","bottom",...k]);function S(t,e){-1===t.indexOf(e)&&t.push(e)}function A(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class M{constructor(){this.subscriptions=[]}add(t){return S(this.subscriptions,t),()=>A(this.subscriptions,t)}notify(t,e,i){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](t,e,i);else for(let s=0;s<r;s++){let r=this.subscriptions[s];r&&r(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function j(){r=void 0}let C={now:()=>(void 0===r&&C.set(b.isProcessing||p.useManualTiming?b.timestamp:performance.now()),r),set:t=>{r=t,queueMicrotask(j)}},E=t=>!isNaN(parseFloat(t)),V={current:void 0};class D{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=C.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=C.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=E(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new M);let i=this.events[t].add(e);return"change"===t?()=>{i(),v.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return V.current&&V.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=C.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function R(t,e){return new D(t,e)}let L=t=>Array.isArray(t),N=t=>!!(t&&t.getVelocity);function F(t,e){let i=t.getValue("willChange");if(N(i)&&i.add)return i.add(e);if(!i&&p.WillChange){let i=new p.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let B=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),O="data-"+B("framerAppearId"),z=(t,e)=>i=>e(t(i)),I=(...t)=>t.reduce(z),U=(t,e,i)=>i>e?e:i<t?t:i,$=t=>1e3*t,W=t=>t/1e3,q={layout:0,mainThread:0,waapi:0},H=()=>{},Y=()=>{},X=t=>e=>"string"==typeof e&&e.startsWith(t),G=X("--"),_=X("var(--"),K=t=>!!_(t)&&Z.test(t.split("/*")[0].trim()),Z=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Q={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},J={...Q,transform:t=>U(0,1,t)},tt={...Q,default:1},te=t=>Math.round(1e5*t)/1e5,ti=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tr=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ts=(t,e)=>i=>!!("string"==typeof i&&tr.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tn=(t,e,i)=>r=>{if("string"!=typeof r)return r;let[s,n,o,a]=r.match(ti);return{[t]:parseFloat(s),[e]:parseFloat(n),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},to=t=>U(0,255,t),ta={...Q,transform:t=>Math.round(to(t))},tl={test:ts("rgb","red"),parse:tn("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:r=1})=>"rgba("+ta.transform(t)+", "+ta.transform(e)+", "+ta.transform(i)+", "+te(J.transform(r))+")"},tu={test:ts("#"),parse:function(t){let e="",i="",r="",s="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),r=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),r=t.substring(3,4),s=t.substring(4,5),e+=e,i+=i,r+=r,s+=s),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:s?parseInt(s,16)/255:1}},transform:tl.transform},th=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),td=th("deg"),tc=th("%"),tm=th("px"),tp=th("vh"),tf=th("vw"),tg={...tc,parse:t=>tc.parse(t)/100,transform:t=>tc.transform(100*t)},ty={test:ts("hsl","hue"),parse:tn("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:r=1})=>"hsla("+Math.round(t)+", "+tc.transform(te(e))+", "+tc.transform(te(i))+", "+te(J.transform(r))+")"},tv={test:t=>tl.test(t)||tu.test(t)||ty.test(t),parse:t=>tl.test(t)?tl.parse(t):ty.test(t)?ty.parse(t):tu.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tl.transform(t):ty.transform(t)},tx=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tb="number",tw="color",tk=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tP(t){let e=t.toString(),i=[],r={color:[],number:[],var:[]},s=[],n=0,o=e.replace(tk,t=>(tv.test(t)?(r.color.push(n),s.push(tw),i.push(tv.parse(t))):t.startsWith("var(")?(r.var.push(n),s.push("var"),i.push(t)):(r.number.push(n),s.push(tb),i.push(parseFloat(t))),++n,"${}")).split("${}");return{values:i,split:o,indexes:r,types:s}}function tT(t){return tP(t).values}function tS(t){let{split:e,types:i}=tP(t),r=e.length;return t=>{let s="";for(let n=0;n<r;n++)if(s+=e[n],void 0!==t[n]){let e=i[n];e===tb?s+=te(t[n]):e===tw?s+=tv.transform(t[n]):s+=t[n]}return s}}let tA=t=>"number"==typeof t?0:t,tM={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(ti)?.length||0)+(t.match(tx)?.length||0)>0},parse:tT,createTransformer:tS,getAnimatableNone:function(t){let e=tT(t);return tS(t)(e.map(tA))}};function tj(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tC(t,e){return i=>i>0?e:t}let tE=(t,e,i)=>t+(e-t)*i,tV=(t,e,i)=>{let r=t*t,s=i*(e*e-r)+r;return s<0?0:Math.sqrt(s)},tD=[tu,tl,ty],tR=t=>tD.find(e=>e.test(t));function tL(t){let e=tR(t);if(H(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===ty&&(i=function({hue:t,saturation:e,lightness:i,alpha:r}){t/=360,i/=100;let s=0,n=0,o=0;if(e/=100){let r=i<.5?i*(1+e):i+e-i*e,a=2*i-r;s=tj(a,r,t+1/3),n=tj(a,r,t),o=tj(a,r,t-1/3)}else s=n=o=i;return{red:Math.round(255*s),green:Math.round(255*n),blue:Math.round(255*o),alpha:r}}(i)),i}let tN=(t,e)=>{let i=tL(t),r=tL(e);if(!i||!r)return tC(t,e);let s={...i};return t=>(s.red=tV(i.red,r.red,t),s.green=tV(i.green,r.green,t),s.blue=tV(i.blue,r.blue,t),s.alpha=tE(i.alpha,r.alpha,t),tl.transform(s))},tF=new Set(["none","hidden"]);function tB(t,e){return i=>tE(t,e,i)}function tO(t){return"number"==typeof t?tB:"string"==typeof t?K(t)?tC:tv.test(t)?tN:tU:Array.isArray(t)?tz:"object"==typeof t?tv.test(t)?tN:tI:tC}function tz(t,e){let i=[...t],r=i.length,s=t.map((t,i)=>tO(t)(t,e[i]));return t=>{for(let e=0;e<r;e++)i[e]=s[e](t);return i}}function tI(t,e){let i={...t,...e},r={};for(let s in i)void 0!==t[s]&&void 0!==e[s]&&(r[s]=tO(t[s])(t[s],e[s]));return t=>{for(let e in r)i[e]=r[e](t);return i}}let tU=(t,e)=>{let i=tM.createTransformer(e),r=tP(t),s=tP(e);return r.indexes.var.length===s.indexes.var.length&&r.indexes.color.length===s.indexes.color.length&&r.indexes.number.length>=s.indexes.number.length?tF.has(t)&&!s.values.length||tF.has(e)&&!r.values.length?function(t,e){return tF.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):I(tz(function(t,e){let i=[],r={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){let n=e.types[s],o=t.indexes[n][r[n]],a=t.values[o]??0;i[s]=a,r[n]++}return i}(r,s),s.values),i):(H(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tC(t,e))};function t$(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tE(t,e,i):tO(t)(t,e)}let tW=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>v.update(e,t),stop:()=>x(e),now:()=>b.isProcessing?b.timestamp:C.now()}},tq=(t,e,i=10)=>{let r="",s=Math.max(Math.round(e/i),2);for(let e=0;e<s;e++)r+=t(e/(s-1))+", ";return`linear(${r.substring(0,r.length-2)})`};function tH(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tY(t,e,i){var r,s;let n=Math.max(e-5,0);return r=i-t(n),(s=e-n)?1e3/s*r:0}let tX={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tG(t,e){return t*Math.sqrt(1-e*e)}let t_=["duration","bounce"],tK=["stiffness","damping","mass"];function tZ(t,e){return e.some(e=>void 0!==t[e])}function tQ(t=tX.visualDuration,e=tX.bounce){let i,r="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:s,restDelta:n}=r,o=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:m,isResolvedFromDuration:p}=function(t){let e={velocity:tX.velocity,stiffness:tX.stiffness,damping:tX.damping,mass:tX.mass,isResolvedFromDuration:!1,...t};if(!tZ(t,tK)&&tZ(t,t_))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),r=i*i,s=2*U(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:tX.mass,stiffness:r,damping:s}}else{let i=function({duration:t=tX.duration,bounce:e=tX.bounce,velocity:i=tX.velocity,mass:r=tX.mass}){let s,n;H(t<=$(tX.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=U(tX.minDamping,tX.maxDamping,o),t=U(tX.minDuration,tX.maxDuration,W(t)),o<1?(s=e=>{let r=e*o,s=r*t;return .001-(r-i)/tG(e,o)*Math.exp(-s)},n=e=>{let r=e*o*t,n=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-r),l=tG(Math.pow(e,2),o);return(r*i+i-n)*a*(-s(e)+.001>0?-1:1)/l}):(s=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),n=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let r=i;for(let i=1;i<12;i++)r-=t(r)/e(r);return r}(s,n,5/t);if(t=$(t),isNaN(a))return{stiffness:tX.stiffness,damping:tX.damping,duration:t};{let e=Math.pow(a,2)*r;return{stiffness:e,damping:2*o*Math.sqrt(r*e),duration:t}}}(t);(e={...e,...i,mass:tX.mass}).isResolvedFromDuration=!0}return e}({...r,velocity:-W(r.velocity||0)}),f=m||0,g=h/(2*Math.sqrt(u*d)),y=a-o,v=W(Math.sqrt(u/d)),x=5>Math.abs(y);if(s||(s=x?tX.restSpeed.granular:tX.restSpeed.default),n||(n=x?tX.restDelta.granular:tX.restDelta.default),g<1){let t=tG(v,g);i=e=>a-Math.exp(-g*v*e)*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-v*t)*(y+(f+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),r=Math.min(t*e,300);return a-i*((f+g*v*y)*Math.sinh(r)+t*y*Math.cosh(r))/t}}let b={calculatedDuration:p&&c||null,next:t=>{let e=i(t);if(p)l.done=t>=c;else{let r=0===t?f:0;g<1&&(r=0===t?$(f):tY(i,t,e));let o=Math.abs(a-e)<=n;l.done=Math.abs(r)<=s&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(tH(b),2e4),e=tq(e=>b.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return b}function tJ({keyframes:t,velocity:e=0,power:i=.8,timeConstant:r=325,bounceDamping:s=10,bounceStiffness:n=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c,m=t[0],p={done:!1,value:m},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,y=i*e,v=m+y,x=void 0===o?v:o(v);x!==v&&(y=x-m);let b=t=>-y*Math.exp(-t/r),w=t=>x+b(t),k=t=>{let e=b(t),i=w(t);p.done=Math.abs(e)<=u,p.value=p.done?x:i},P=t=>{f(p.value)&&(d=t,c=tQ({keyframes:[p.value,g(p.value)],velocity:tY(w,t,p.value),damping:s,stiffness:n,restDelta:u,restSpeed:h}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,k(t),P(t)),void 0!==d&&t>=d)?c.next(t-d):(e||k(t),p)}}}tQ.applyToOptions=t=>{let e=function(t,e=100,i){let r=i({...t,keyframes:[0,e]}),s=Math.min(tH(r),2e4);return{type:"keyframes",ease:t=>r.next(s*t).value/e,duration:W(s)}}(t,100,tQ);return t.ease=e.ease,t.duration=$(e.duration),t.type="keyframes",t};let t0=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function t1(t,e,i,r){if(t===e&&i===r)return m;let s=e=>(function(t,e,i,r,s){let n,o,a=0;do(n=t0(o=e+(i-e)/2,r,s)-t)>0?i=o:e=o;while(Math.abs(n)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:t0(s(t),e,r)}let t2=t1(.42,0,1,1),t5=t1(0,0,.58,1),t3=t1(.42,0,.58,1),t6=t=>Array.isArray(t)&&"number"!=typeof t[0],t4=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t9=t=>e=>1-t(1-e),t7=t1(.33,1.53,.69,.99),t8=t9(t7),et=t4(t8),ee=t=>(t*=2)<1?.5*t8(t):.5*(2-Math.pow(2,-10*(t-1))),ei=t=>1-Math.sin(Math.acos(t)),er=t9(ei),es=t4(ei),en=t=>Array.isArray(t)&&"number"==typeof t[0],eo={linear:m,easeIn:t2,easeInOut:t3,easeOut:t5,circIn:ei,circInOut:es,circOut:er,backIn:t8,backInOut:et,backOut:t7,anticipate:ee},ea=t=>"string"==typeof t,el=t=>{if(en(t)){Y(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,r,s]=t;return t1(e,i,r,s)}return ea(t)?(Y(void 0!==eo[t],`Invalid easing type '${t}'`),eo[t]):t},eu=(t,e,i)=>{let r=e-t;return 0===r?1:(i-t)/r};function eh({duration:t=300,keyframes:e,times:i,ease:r="easeInOut"}){var s;let n=t6(r)?r.map(el):el(r),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:r,mixer:s}={}){let n=t.length;if(Y(n===e.length,"Both input and output ranges must be the same length"),1===n)return()=>e[0];if(2===n&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[n-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let r=[],s=i||p.mix||t$,n=t.length-1;for(let i=0;i<n;i++){let n=s(t[i],t[i+1]);e&&(n=I(Array.isArray(e)?e[i]||m:e,n)),r.push(n)}return r}(e,r,s),l=a.length,u=i=>{if(o&&i<t[0])return e[0];let r=0;if(l>1)for(;r<t.length-2&&!(i<t[r+1]);r++);let s=eu(t[r],t[r+1],i);return a[r](s)};return i?e=>u(U(t[0],t[n-1],e)):u}((s=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let r=1;r<=e;r++){let s=eu(0,e,r);t.push(tE(i,1,s))}}(e,t.length-1),e}(e),s.map(e=>e*t)),e,{ease:Array.isArray(n)?n:e.map(()=>n||t3).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let ed=t=>null!==t;function ec(t,{repeat:e,repeatType:i="loop"},r,s=1){let n=t.filter(ed),o=s<0||e&&"loop"!==i&&e%2==1?0:n.length-1;return o&&void 0!==r?r:n[o]}let em={decay:tJ,inertia:tJ,tween:eh,keyframes:eh,spring:tQ};function ep(t){"string"==typeof t.type&&(t.type=em[t.type])}class ef{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let eg=t=>t/100;class ey extends ef{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==C.now()&&this.tick(C.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},q.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;ep(t);let{type:e=eh,repeat:i=0,repeatDelay:r=0,repeatType:s,velocity:n=0}=t,{keyframes:o}=t,a=e||eh;a!==eh&&"number"!=typeof o[0]&&(this.mixKeyframes=I(eg,t$(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===s&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-n})),null===l.calculatedDuration&&(l.calculatedDuration=tH(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(i+1)-r,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:r,mixKeyframes:s,mirroredGenerator:n,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:d,repeatDelay:c,type:m,onUpdate:p,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-r/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let v=this.currentTime,x=i;if(h){let t=Math.min(this.currentTime,r)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/o)):"mirror"===d&&(x=n)),v=U(0,1,i)*o}let b=y?{done:!1,value:u[0]}:x.next(v);s&&(b.value=s(b.value));let{done:w}=b;y||null===a||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let k=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return k&&m!==tJ&&(b.value=ec(u,this.options,f,this.speed)),p&&p(b.value),k&&this.finish(),b}then(t,e){return this.finished.then(t,e)}get duration(){return W(this.calculatedDuration)}get time(){return W(this.currentTime)}set time(t){t=$(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(C.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=W(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tW,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(C.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,q.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let ev=t=>180*t/Math.PI,ex=t=>ew(ev(Math.atan2(t[1],t[0]))),eb={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:ex,rotateZ:ex,skewX:t=>ev(Math.atan(t[1])),skewY:t=>ev(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ew=t=>((t%=360)<0&&(t+=360),t),ek=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),eP=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),eT={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ek,scaleY:eP,scale:t=>(ek(t)+eP(t))/2,rotateX:t=>ew(ev(Math.atan2(t[6],t[5]))),rotateY:t=>ew(ev(Math.atan2(-t[2],t[0]))),rotateZ:ex,rotate:ex,skewX:t=>ev(Math.atan(t[4])),skewY:t=>ev(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function eS(t){return+!!t.includes("scale")}function eA(t,e){let i,r;if(!t||"none"===t)return eS(e);let s=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(s)i=eT,r=s;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=eb,r=e}if(!r)return eS(e);let n=i[e],o=r[1].split(",").map(ej);return"function"==typeof n?n(o):o[n]}let eM=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return eA(i,e)};function ej(t){return parseFloat(t.trim())}let eC=t=>t===Q||t===tm,eE=new Set(["x","y","z"]),eV=k.filter(t=>!eE.has(t)),eD={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eA(e,"x"),y:(t,{transform:e})=>eA(e,"y")};eD.translateX=eD.x,eD.translateY=eD.y;let eR=new Set,eL=!1,eN=!1,eF=!1;function eB(){if(eN){let t=Array.from(eR).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return eV.forEach(i=>{let r=t.getValue(i);void 0!==r&&(e.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eN=!1,eL=!1,eR.forEach(t=>t.complete(eF)),eR.clear()}function eO(){eR.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eN=!0)})}class ez{constructor(t,e,i,r,s,n=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=r,this.element=s,this.isAsync=n}scheduleResolve(){this.state="scheduled",this.isAsync?(eR.add(this),eL||(eL=!0,v.read(eO),v.resolveKeyframes(eB))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:r}=this;if(null===t[0]){let s=r?.get(),n=t[t.length-1];if(void 0!==s)t[0]=s;else if(i&&e){let r=i.readValue(e,n);null!=r&&(t[0]=r)}void 0===t[0]&&(t[0]=n),r&&void 0===s&&r.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eR.delete(this)}cancel(){"scheduled"===this.state&&(eR.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eI=t=>t.startsWith("--");function eU(t){let e;return()=>(void 0===e&&(e=t()),e)}let e$=eU(()=>void 0!==window.ScrollTimeline),eW={},eq=function(t,e){let i=eU(t);return()=>eW[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eH=([t,e,i,r])=>`cubic-bezier(${t}, ${e}, ${i}, ${r})`,eY={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eH([0,.65,.55,1]),circOut:eH([.55,0,1,.45]),backIn:eH([.31,.01,.66,-.59]),backOut:eH([.33,1.53,.69,.99])};function eX(t){return"function"==typeof t&&"applyToOptions"in t}class eG extends ef{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:r,pseudoElement:s,allowFlatten:n=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!s,this.allowFlatten=n,this.options=t,Y("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return eX(t)&&eq()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:r=0,duration:s=300,repeat:n=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?eq()?tq(e,i):"ease-out":en(e)?eH(e):Array.isArray(e)?e.map(e=>t(e,i)||eY.easeOut):eY[e]}(a,s);Array.isArray(d)&&(h.easing=d),g.value&&q.waapi++;let c={delay:r,duration:s,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:n+1,direction:"reverse"===o?"alternate":"normal"};u&&(c.pseudoElement=u);let m=t.animate(h,c);return g.value&&m.finished.finally(()=>{q.waapi--}),m}(e,i,r,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){let t=ec(r,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eI(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return W(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return W(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=$(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&e$())?(this.animation.timeline=t,m):e(this)}}let e_={anticipate:ee,backInOut:et,circInOut:es};class eK extends eG{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in e_&&(t.ease=e_[t.ease])}(t),ep(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:r,element:s,...n}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new ey({...n,autoplay:!1}),a=$(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let eZ=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tM.test(t)||"0"===t)&&!t.startsWith("url("));function eQ(t){return"object"==typeof t&&null!==t}function eJ(t){return eQ(t)&&"offsetHeight"in t}let e0=new Set(["opacity","clipPath","filter","transform"]),e1=eU(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class e2 extends ef{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:r=0,repeatDelay:s=0,repeatType:n="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=C.now();let d={autoplay:t,delay:e,type:i,repeat:r,repeatDelay:s,repeatType:n,name:a,motionValue:l,element:u,...h},c=u?.KeyframeResolver||ez;this.keyframeResolver=new c(o,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,r){this.keyframeResolver=void 0;let{name:s,type:n,velocity:o,delay:a,isHandoff:l,onUpdate:u}=i;this.resolvedAt=C.now(),!function(t,e,i,r){let s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;let n=t[t.length-1],o=eZ(s,e),a=eZ(n,e);return H(o===a,`You are trying to animate ${e} from "${s}" to "${n}". ${s} is not an animatable value - to enable this animation set ${s} to a value animatable to ${n} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||eX(i))&&r)}(t,s,n,o)&&((p.instantAnimations||!a)&&u?.(ec(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let h={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:r,repeatType:s,damping:n,type:o}=t;if(!eJ(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return e1()&&i&&e0.has(i)&&("transform"!==i||!l)&&!a&&!r&&"mirror"!==s&&0!==n&&"inertia"!==o}(h)?new eK({...h,element:h.motionValue.owner.current}):new ey(h);d.finished.then(()=>this.notifyFinished()).catch(m),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eF=!0,eO(),eB(),eF=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e5=t=>null!==t,e3={type:"spring",stiffness:500,damping:25,restSpeed:10},e6=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e4={type:"keyframes",duration:.8},e9={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e7=(t,{keyframes:e})=>e.length>2?e4:P.has(t)?t.startsWith("scale")?e6(e[1]):e3:e9,e8=(t,e,i,r={},s,n)=>o=>{let a=c(r,t)||{},l=a.delay||r.delay||0,{elapsed:u=0}=r;u-=$(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:n?void 0:s};!function({when:t,delay:e,delayChildren:i,staggerChildren:r,staggerDirection:s,repeat:n,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&Object.assign(h,e7(t,h)),h.duration&&(h.duration=$(h.duration)),h.repeatDelay&&(h.repeatDelay=$(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(d=!0)),(p.instantAnimations||p.skipAnimations)&&(d=!0,h.duration=0,h.delay=0),h.allowFlatten=!a.type&&!a.ease,d&&!n&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},r){let s=t.filter(e5),n=e&&"loop"!==i&&e%2==1?0:s.length-1;return s[n]}(h.keyframes,a);if(void 0!==t)return void v.update(()=>{h.onUpdate(t),h.onComplete()})}return a.isSync?new ey(h):new e2(h)};function it(t,e,{delay:i=0,transitionOverride:r,type:s}={}){let{transition:n=t.getDefaultTransition(),transitionEnd:o,...a}=e;r&&(n=r);let l=[],u=s&&t.animationState&&t.animationState.getState()[s];for(let e in a){let r=t.getValue(e,t.latestValues[e]??null),s=a[e];if(void 0===s||u&&function({protectedKeys:t,needsAnimating:e},i){let r=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,r}(u,e))continue;let o={delay:i,...c(n||{},e)},h=r.get();if(void 0!==h&&!r.isAnimating&&!Array.isArray(s)&&s===h&&!o.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let i=t.props[O];if(i){let t=window.MotionHandoffAnimation(i,e,v);null!==t&&(o.startTime=t,d=!0)}}F(t,e),r.start(e8(e,r,s,t.shouldReduceMotion&&T.has(e)?{type:!1}:o,t,d));let m=r.animation;m&&l.push(m)}return o&&Promise.all(l).then(()=>{v.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:r={},...s}=d(t,e)||{};for(let e in s={...s,...i}){var n;let i=L(n=s[e])?n[n.length-1]||0:n;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,R(i))}}(t,o)})}),l}function ie(t,e,i={}){let r=d(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(s=i.transitionOverride);let n=r?()=>Promise.all(it(t,r,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(r=0)=>{let{delayChildren:n=0,staggerChildren:o,staggerDirection:a}=s;return function(t,e,i=0,r=0,s=1,n){let o=[],a=(t.variantChildren.size-1)*r,l=1===s?(t=0)=>t*r:(t=0)=>a-t*r;return Array.from(t.variantChildren).sort(ii).forEach((t,r)=>{t.notify("AnimationStart",e),o.push(ie(t,e,{...n,delay:i+l(r)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,n+r,o,a,i)}:()=>Promise.resolve(),{when:a}=s;if(!a)return Promise.all([n(),o(i.delay)]);{let[t,e]="beforeChildren"===a?[n,o]:[o,n];return t().then(()=>e())}}function ii(t,e){return t.sortNodePosition(e)}function ir(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let r=0;r<i;r++)if(e[r]!==t[r])return!1;return!0}function is(t){return"string"==typeof t||Array.isArray(t)}let io=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ia=["initial",...io],il=ia.length,iu=[...io].reverse(),ih=io.length;function id(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ic(){return{animate:id(!0),whileInView:id(),whileHover:id(),whileTap:id(),whileDrag:id(),whileFocus:id(),exit:id()}}class im{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ip extends im{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>ie(t,e,i)));else if("string"==typeof e)r=ie(t,e,i);else{let s="function"==typeof e?d(t,e,i.custom):e;r=Promise.all(it(t,s,i))}return r.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=ic(),r=!0,s=e=>(i,r)=>{let s=d(t,r,"exit"===e?t.presenceContext?.custom:void 0);if(s){let{transition:t,transitionEnd:e,...r}=s;i={...i,...r,...e}}return i};function n(n){let{props:o}=t,a=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<il;t++){let r=ia[t],s=e.props[r];(is(s)||!1===s)&&(i[r]=s)}return i}(t.parent)||{},u=[],h=new Set,c={},m=1/0;for(let e=0;e<ih;e++){var p,f;let d=iu[e],g=i[d],y=void 0!==o[d]?o[d]:a[d],v=is(y),x=d===n?g.isActive:null;!1===x&&(m=e);let b=y===a[d]&&y!==o[d]&&v;if(b&&r&&t.manuallyAnimateOnMount&&(b=!1),g.protectedKeys={...c},!g.isActive&&null===x||!y&&!g.prevProp||l(y)||"boolean"==typeof y)continue;let w=(p=g.prevProp,"string"==typeof(f=y)?f!==p:!!Array.isArray(f)&&!ir(f,p)),k=w||d===n&&g.isActive&&!b&&v||e>m&&v,P=!1,T=Array.isArray(y)?y:[y],S=T.reduce(s(d),{});!1===x&&(S={});let{prevResolvedValues:A={}}=g,M={...A,...S},j=e=>{k=!0,h.has(e)&&(P=!0,h.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in M){let e=S[t],i=A[t];if(c.hasOwnProperty(t))continue;let r=!1;(L(e)&&L(i)?ir(e,i):e===i)?void 0!==e&&h.has(t)?j(t):g.protectedKeys[t]=!0:null!=e?j(t):h.add(t)}g.prevProp=y,g.prevResolvedValues=S,g.isActive&&(c={...c,...S}),r&&t.blockInitialAnimation&&(k=!1);let C=!(b&&w)||P;k&&C&&u.push(...T.map(t=>({animation:t,options:{type:d}})))}if(h.size){let e={};if("boolean"!=typeof o.initial){let i=d(t,Array.isArray(o.initial)?o.initial[0]:o.initial);i&&i.transition&&(e.transition=i.transition)}h.forEach(i=>{let r=t.getBaseTarget(i),s=t.getValue(i);s&&(s.liveStyle=!0),e[i]=r??null}),u.push({animation:e})}let g=!!u.length;return r&&(!1===o.initial||o.initial===o.animate)&&!t.manuallyAnimateOnMount&&(g=!1),r=!1,g?e(u):Promise.resolve()}return{animateChanges:n,setActive:function(e,r){if(i[e].isActive===r)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,r)),i[e].isActive=r;let s=n(e);for(let t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=ic(),r=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();l(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ig=0;class iy extends im{constructor(){super(...arguments),this.id=ig++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let r=this.node.animationState.setActive("exit",!t);e&&!t&&r.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let iv={x:!1,y:!1};function ix(t,e,i,r={passive:!0}){return t.addEventListener(e,i,r),()=>t.removeEventListener(e,i)}let ib=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iw(t){return{point:{x:t.pageX,y:t.pageY}}}let ik=t=>e=>ib(e)&&t(e,iw(e));function iP(t,e,i,r){return ix(t,e,ik(i),r)}function iT({top:t,left:e,right:i,bottom:r}){return{x:{min:e,max:i},y:{min:t,max:r}}}function iS(t){return t.max-t.min}function iA(t,e,i,r=.5){t.origin=r,t.originPoint=tE(e.min,e.max,t.origin),t.scale=iS(i)/iS(e),t.translate=tE(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iM(t,e,i,r){iA(t.x,e.x,i.x,r?r.originX:void 0),iA(t.y,e.y,i.y,r?r.originY:void 0)}function ij(t,e,i){t.min=i.min+e.min,t.max=t.min+iS(e)}function iC(t,e,i){t.min=e.min-i.min,t.max=t.min+iS(e)}function iE(t,e,i){iC(t.x,e.x,i.x),iC(t.y,e.y,i.y)}let iV=()=>({translate:0,scale:1,origin:0,originPoint:0}),iD=()=>({x:iV(),y:iV()}),iR=()=>({min:0,max:0}),iL=()=>({x:iR(),y:iR()});function iN(t){return[t("x"),t("y")]}function iF(t){return void 0===t||1===t}function iB({scale:t,scaleX:e,scaleY:i}){return!iF(t)||!iF(e)||!iF(i)}function iO(t){return iB(t)||iz(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iz(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iI(t,e,i,r,s){return void 0!==s&&(t=r+s*(t-r)),r+i*(t-r)+e}function iU(t,e=0,i=1,r,s){t.min=iI(t.min,e,i,r,s),t.max=iI(t.max,e,i,r,s)}function i$(t,{x:e,y:i}){iU(t.x,e.translate,e.scale,e.originPoint),iU(t.y,i.translate,i.scale,i.originPoint)}function iW(t,e){t.min=t.min+e,t.max=t.max+e}function iq(t,e,i,r,s=.5){let n=tE(t.min,t.max,s);iU(t,e,i,n,r)}function iH(t,e){iq(t.x,e.x,e.scaleX,e.scale,e.originX),iq(t.y,e.y,e.scaleY,e.scale,e.originY)}function iY(t,e){return iT(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(t.getBoundingClientRect(),e))}let iX=({current:t})=>t?t.ownerDocument.defaultView:null;function iG(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let i_=(t,e)=>Math.abs(t-e);class iK{constructor(t,e,{transformPagePoint:i,contextWindow:r,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iJ(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(i_(t.x,e.x)**2+i_(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:r}=t,{timestamp:s}=b;this.history.push({...r,timestamp:s});let{onStart:n,onMove:o}=this.handlers;e||(n&&n(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iZ(e,this.transformPagePoint),v.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let n=iJ("pointercancel"===t.type?this.lastMoveEventInfo:iZ(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,n),r&&r(t,n)},!ib(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=i,this.contextWindow=r||window;let n=iZ(iw(t),this.transformPagePoint),{point:o}=n,{timestamp:a}=b;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iJ(n,this.history)),this.removeListeners=I(iP(this.contextWindow,"pointermove",this.handlePointerMove),iP(this.contextWindow,"pointerup",this.handlePointerUp),iP(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),x(this.updatePoint)}}function iZ(t,e){return e?{point:e(t.point)}:t}function iQ(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iJ({point:t},e){return{point:t,delta:iQ(t,i0(e)),offset:iQ(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,r=null,s=i0(t);for(;i>=0&&(r=t[i],!(s.timestamp-r.timestamp>$(.1)));)i--;if(!r)return{x:0,y:0};let n=W(s.timestamp-r.timestamp);if(0===n)return{x:0,y:0};let o={x:(s.x-r.x)/n,y:(s.y-r.y)/n};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function i0(t){return t[t.length-1]}function i1(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function i2(t,e){let i=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,r]=[r,i]),{min:i,max:r}}function i5(t,e,i){return{min:i3(t,e),max:i3(t,i)}}function i3(t,e){return"number"==typeof t?t:t[e]||0}let i6=new WeakMap;class i4{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iL(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new iK(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iw(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:r,onDragStart:s}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(iv[t])return null;else return iv[t]=!0,()=>{iv[t]=!1};return iv.x||iv.y?null:(iv.x=iv.y=!0,()=>{iv.x=iv.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iN(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tc.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[t];r&&(e=iS(r)*(parseFloat(e)/100))}}this.originPoint[t]=e}),s&&v.postRender(()=>s(t,e)),F(this.visualElement,"transform");let{animationState:n}=this.visualElement;n&&n.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:s,onDrag:n}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(r&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&s&&s(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),n&&n(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iN(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:iX(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:r}=e;this.startAnimation(r);let{onDragEnd:s}=this.getProps();s&&v.postRender(()=>s(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:r}=this.getProps();if(!i||!i9(t,r,this.currentDirection))return;let s=this.getAxisMotionValue(t),n=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(n=function(t,{min:e,max:i},r){return void 0!==e&&t<e?t=r?tE(e,t,r.min):Math.max(t,e):void 0!==i&&t>i&&(t=r?tE(i,t,r.max):Math.min(t,i)),t}(n,this.constraints[t],this.elastic[t])),s.set(n)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;t&&iG(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:r,right:s}){return{x:i1(t.x,i,s),y:i1(t.y,e,r)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i5(t,"left","right"),y:i5(t,"top","bottom")}}(e),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iN(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iG(e))return!1;let r=e.current;Y(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:s}=this.visualElement;if(!s||!s.layout)return!1;let n=function(t,e,i){let r=iY(t,i),{scroll:s}=e;return s&&(iW(r.x,s.offset.x),iW(r.y,s.offset.y)),r}(r,s.root,this.visualElement.getTransformPagePoint()),o=(t=s.layout.layoutBox,{x:i2(t.x,n.x),y:i2(t.y,n.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iT(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:r,dragTransition:s,dragSnapToOrigin:n,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iN(o=>{if(!i9(o,e,this.currentDirection))return;let l=a&&a[o]||{};n&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return F(this.visualElement,t),i.start(e8(t,i,0,e,this.visualElement,!1))}stopAnimation(){iN(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iN(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iN(e=>{let{drag:i}=this.getProps();if(!i9(e,i,this.currentDirection))return;let{projection:r}=this.visualElement,s=this.getAxisMotionValue(e);if(r&&r.layout){let{min:i,max:n}=r.layout.layoutBox[e];s.set(t[e]-tE(i,n,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iG(e)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};iN(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();r[t]=function(t,e){let i=.5,r=iS(t),s=iS(e);return s>r?i=eu(e.min,e.max-r,t.min):r>s&&(i=eu(t.min,t.max-s,e.min)),U(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iN(e=>{if(!i9(e,t,null))return;let i=this.getAxisMotionValue(e),{min:s,max:n}=this.constraints[e];i.set(tE(s,n,r[e]))})}addListeners(){if(!this.visualElement.current)return;i6.set(this.visualElement,this);let t=iP(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iG(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),v.read(e);let s=ix(window,"resize",()=>this.scalePositionWithinConstraints()),n=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iN(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),r(),n&&n()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:s=!1,dragElastic:n=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:r,dragConstraints:s,dragElastic:n,dragMomentum:o}}}function i9(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i7 extends im{constructor(t){super(t),this.removeGroupControls=m,this.removeListeners=m,this.controls=new i4(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||m}unmount(){this.removeGroupControls(),this.removeListeners()}}let i8=t=>(e,i)=>{t&&v.postRender(()=>t(e,i))};class rt extends im{constructor(){super(...arguments),this.removePointerDownListener=m}onPointerDown(t){this.session=new iK(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iX(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:i8(t),onStart:i8(e),onMove:i,onEnd:(t,e)=>{delete this.session,r&&v.postRender(()=>r(t,e))}}}mount(){this.removePointerDownListener=iP(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let{schedule:re}=y(queueMicrotask,!1),ri=(0,a.createContext)(null);function rr(t=!0){let e=(0,a.useContext)(ri);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:r,register:s}=e,n=(0,a.useId)();(0,a.useEffect)(()=>{if(t)return s(n)},[t]);let o=(0,a.useCallback)(()=>t&&r&&r(n),[n,r,t]);return!i&&r?[!1,o]:[!0]}let rs=(0,a.createContext)({}),rn=(0,a.createContext)({}),ro={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ra(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let rl={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tm.test(t))return t;else t=parseFloat(t);let i=ra(t,e.target.x),r=ra(t,e.target.y);return`${i}% ${r}%`}},ru={};class rh extends a.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:r}=this.props,{projection:s}=t;for(let t in rc)ru[t]=rc[t],G(t)&&(ru[t].isCSSVariable=!0);s&&(e.group&&e.group.add(s),i&&i.register&&r&&i.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),ro.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:r,isPresent:s}=this.props,{projection:n}=i;return n&&(n.isPresent=s,r||t.layoutDependency!==e||void 0===e||t.isPresent!==s?n.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?n.promote():n.relegate()||v.postRender(()=>{let t=n.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),re.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function rd(t){let[e,i]=rr(),r=(0,a.useContext)(rs);return(0,o.jsx)(rh,{...t,layoutGroup:r,switchLayoutGroup:(0,a.useContext)(rn),isPresent:e,safeToRemove:i})}let rc={borderRadius:{...rl,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:rl,borderTopRightRadius:rl,borderBottomLeftRadius:rl,borderBottomRightRadius:rl,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let r=tM.parse(t);if(r.length>5)return t;let s=tM.createTransformer(t),n=+("number"!=typeof r[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;r[0+n]/=o,r[1+n]/=a;let l=tE(o,a,.5);return"number"==typeof r[2+n]&&(r[2+n]/=l),"number"==typeof r[3+n]&&(r[3+n]/=l),s(r)}}};function rm(t){return eQ(t)&&"ownerSVGElement"in t}let rp=(t,e)=>t.depth-e.depth;class rf{constructor(){this.children=[],this.isDirty=!1}add(t){S(this.children,t),this.isDirty=!0}remove(t){A(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(rp),this.isDirty=!1,this.children.forEach(t)}}function rg(t){return N(t)?t.get():t}let ry=["TopLeft","TopRight","BottomLeft","BottomRight"],rv=ry.length,rx=t=>"string"==typeof t?parseFloat(t):t,rb=t=>"number"==typeof t||tm.test(t);function rw(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let rk=rT(0,.5,er),rP=rT(.5,.95,m);function rT(t,e,i){return r=>r<t?0:r>e?1:i(eu(t,e,r))}function rS(t,e){t.min=e.min,t.max=e.max}function rA(t,e){rS(t.x,e.x),rS(t.y,e.y)}function rM(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function rj(t,e,i,r,s){return t-=e,t=r+1/i*(t-r),void 0!==s&&(t=r+1/s*(t-r)),t}function rC(t,e,[i,r,s],n,o){!function(t,e=0,i=1,r=.5,s,n=t,o=t){if(tc.test(e)&&(e=parseFloat(e),e=tE(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=tE(n.min,n.max,r);t===n&&(a-=e),t.min=rj(t.min,e,i,a,s),t.max=rj(t.max,e,i,a,s)}(t,e[i],e[r],e[s],e.scale,n,o)}let rE=["x","scaleX","originX"],rV=["y","scaleY","originY"];function rD(t,e,i,r){rC(t.x,e,rE,i?i.x:void 0,r?r.x:void 0),rC(t.y,e,rV,i?i.y:void 0,r?r.y:void 0)}function rR(t){return 0===t.translate&&1===t.scale}function rL(t){return rR(t.x)&&rR(t.y)}function rN(t,e){return t.min===e.min&&t.max===e.max}function rF(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function rB(t,e){return rF(t.x,e.x)&&rF(t.y,e.y)}function rO(t){return iS(t.x)/iS(t.y)}function rz(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class rI{constructor(){this.members=[]}add(t){S(this.members,t),t.scheduleRender()}remove(t){if(A(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:r}=t.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rU={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},r$=["","X","Y","Z"],rW={visibility:"hidden"},rq=0;function rH(t,e,i,r){let{latestValues:s}=e;s[t]&&(i[t]=s[t],e.setStaticValue(t,0),r&&(r[t]=0))}function rY({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:r,resetTransform:s}){return class{constructor(t={},i=e?.()){this.id=rq++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,g.value&&(rU.nodes=rU.calculatedTargetDeltas=rU.calculatedProjections=0),this.nodes.forEach(r_),this.nodes.forEach(r2),this.nodes.forEach(r5),this.nodes.forEach(rK),g.addProjectionMetrics&&g.addProjectionMetrics(rU)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new rf)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new M),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=rm(e)&&!(rm(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:r,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||i)&&(this.isLayoutDirty=!0),t){let i,r=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=C.now(),r=({timestamp:s})=>{let n=s-i;n>=250&&(x(r),t(n-e))};return v.setup(r,!0),()=>x(r)}(r,250),ro.hasAnimatedSinceResize&&(ro.hasAnimatedSinceResize=!1,this.nodes.forEach(r1))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&s&&(i||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||s.getDefaultTransition()||r8,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=s.getProps(),l=!this.targetLayout||!rB(this.targetLayout,r),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...c(n,"layout"),onPlay:o,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||r1(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),x(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(r3),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let r=i.props[O];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(r,"transform",v,!(t||i))}let{parent:s}=e;s&&!s.hasCheckedOptimisedAppear&&t(s)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rQ);return}this.isUpdating||this.nodes.forEach(rJ),this.isUpdating=!1,this.nodes.forEach(r0),this.nodes.forEach(rX),this.nodes.forEach(rG),this.clearAllSnapshots();let t=C.now();b.delta=U(0,1e3/60,t-b.timestamp),b.timestamp=t,b.isProcessing=!0,w.update.process(b),w.preRender.process(b),w.render.process(b),b.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,re.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rZ),this.sharedNodes.forEach(r6)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,v.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){v.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iS(this.snapshot.measuredBox.x)||iS(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iL(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=r(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!rL(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,n=r!==this.prevTransformTemplateValue;t&&this.instance&&(e||iO(this.latestValues)||n)&&(s(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),si((e=r).x),si(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iL();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(ss))){let{scroll:t}=this.root;t&&(iW(e.x,t.offset.x),iW(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iL();if(rA(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let r=this.path[i],{scroll:s,options:n}=r;r!==this.root&&s&&n.layoutScroll&&(s.wasRoot&&rA(e,t),iW(e.x,s.offset.x),iW(e.y,s.offset.y))}return e}applyTransform(t,e=!1){let i=iL();rA(i,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];!e&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iH(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),iO(r.latestValues)&&iH(i,r.latestValues)}return iO(this.latestValues)&&iH(i,this.latestValues),i}removeTransform(t){let e=iL();rA(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iO(i.latestValues))continue;iB(i.latestValues)&&i.updateSnapshot();let r=iL();rA(r,i.measurePageBox()),rD(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return iO(this.latestValues)&&rD(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==b.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:s}=this.options;if(this.layout&&(r||s)){if(this.resolvedRelativeTargetAt=b.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iL(),this.relativeTargetOrigin=iL(),iE(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),rA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iL(),this.targetWithTransforms=iL()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var n,o,a;this.forceRelativeParentToResolveTarget(),n=this.target,o=this.relativeTarget,a=this.relativeParent.target,ij(n.x,o.x,a.x),ij(n.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rA(this.target,this.layout.layoutBox),i$(this.target,this.targetDelta)):rA(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iL(),this.relativeTargetOrigin=iL(),iE(this.relativeTargetOrigin,this.target,t.target),rA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}g.value&&rU.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iB(this.parent.latestValues)||iz(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===b.timestamp&&(i=!1),i)return;let{layout:r,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||s))return;rA(this.layoutCorrected,this.layout.layoutBox);let n=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,r=!1){let s,n,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){n=(s=i[a]).projectionDelta;let{visualElement:o}=s.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(r&&s.options.layoutScroll&&s.scroll&&s!==s.root&&iH(t,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),n&&(e.x*=n.x.scale,e.y*=n.y.scale,i$(t,n)),r&&iO(s.latestValues)&&iH(t,s.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iL());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rM(this.prevProjectionDelta.x,this.projectionDelta.x),rM(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iM(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===n&&this.treeScale.y===o&&rz(this.projectionDelta.x,this.prevProjectionDelta.x)&&rz(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),g.value&&rU.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iD(),this.projectionDelta=iD(),this.projectionDeltaWithTransform=iD()}setAnimationOrigin(t,e=!1){let i,r=this.snapshot,s=r?r.latestValues:{},n={...this.latestValues},o=iD();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iL(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(r7));this.animationProgress=0,this.mixTargetDelta=e=>{let r=e/1e3;if(r4(o.x,t.x,r),r4(o.y,t.y,r),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,m,p,f,g;iE(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),m=this.relativeTarget,p=this.relativeTargetOrigin,f=a,g=r,r9(m.x,p.x,f.x,g),r9(m.y,p.y,f.y,g),i&&(u=this.relativeTarget,c=i,rN(u.x,c.x)&&rN(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=iL()),rA(i,this.relativeTarget)}l&&(this.animationValues=n,function(t,e,i,r,s,n){s?(t.opacity=tE(0,i.opacity??1,rk(r)),t.opacityExit=tE(e.opacity??1,0,rP(r))):n&&(t.opacity=tE(e.opacity??1,i.opacity??1,r));for(let s=0;s<rv;s++){let n=`border${ry[s]}Radius`,o=rw(e,n),a=rw(i,n);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||rb(o)===rb(a)?(t[n]=Math.max(tE(rx(o),rx(a),r),0),(tc.test(a)||tc.test(o))&&(t[n]+="%")):t[n]=a)}(e.rotate||i.rotate)&&(t.rotate=tE(e.rotate||0,i.rotate||0,r))}(n,s,this.latestValues,r,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(x(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=v.update(()=>{ro.hasAnimatedSinceResize=!0,q.layout++,this.motionValue||(this.motionValue=R(0)),this.currentAnimation=function(t,e,i){let r=N(t)?t:R(t);return r.start(e8("",r,e,i)),r.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{q.layout--},onComplete:()=>{q.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:r,latestValues:s}=t;if(e&&i&&r){if(this!==t&&this.layout&&r&&sr(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||iL();let e=iS(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let r=iS(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+r}rA(e,i),iH(e,s),iM(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new rI),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let r={};i.z&&rH("z",t,r,this.animationValues);for(let e=0;e<r$.length;e++)rH(`rotate${r$[e]}`,t,r,this.animationValues),rH(`skew${r$[e]}`,t,r,this.animationValues);for(let e in t.render(),r)t.setStaticValue(e,r[e]),this.animationValues&&(this.animationValues[e]=r[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return rW;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=rg(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=rg(t?.pointerEvents)||""),this.hasProjected&&!iO(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let s=r.animationValues||r.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let r="",s=t.x.translate/e.x,n=t.y.translate/e.y,o=i?.z||0;if((s||n||o)&&(r=`translate3d(${s}px, ${n}px, ${o}px) `),(1!==e.x||1!==e.y)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:s,rotateY:n,skewX:o,skewY:a}=i;t&&(r=`perspective(${t}px) ${r}`),e&&(r+=`rotate(${e}deg) `),s&&(r+=`rotateX(${s}deg) `),n&&(r+=`rotateY(${n}deg) `),o&&(r+=`skewX(${o}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),i&&(e.transform=i(s,e.transform));let{x:n,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*n.origin}% ${100*o.origin}% 0`,r.animationValues?e.opacity=r===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:e.opacity=r===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,ru){if(void 0===s[t])continue;let{correct:i,applyTo:n,isCSSVariable:o}=ru[t],a="none"===e.transform?s[t]:i(s[t],r);if(n){let t=n.length;for(let i=0;i<t;i++)e[n[i]]=a}else o?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=r===this?rg(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(rQ),this.root.sharedNodes.clear()}}}function rX(t){t.updateLayout()}function rG(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:r}=t.layout,{animationType:s}=t.options,n=e.source!==t.layout.source;"size"===s?iN(t=>{let r=n?e.measuredBox[t]:e.layoutBox[t],s=iS(r);r.min=i[t].min,r.max=r.min+s}):sr(s,e.layoutBox,i)&&iN(r=>{let s=n?e.measuredBox[r]:e.layoutBox[r],o=iS(i[r]);s.max=s.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[r].max=t.relativeTarget[r].min+o)});let o=iD();iM(o,i,e.layoutBox);let a=iD();n?iM(a,t.applyTransform(r,!0),e.measuredBox):iM(a,i,e.layoutBox);let l=!rL(o),u=!1;if(!t.resumeFrom){let r=t.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:s,layout:n}=r;if(s&&n){let o=iL();iE(o,e.layoutBox,s.layoutBox);let a=iL();iE(a,i,n.layoutBox),rB(o,a)||(u=!0),r.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=r)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function r_(t){g.value&&rU.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function rK(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function rZ(t){t.clearSnapshot()}function rQ(t){t.clearMeasurements()}function rJ(t){t.isLayoutDirty=!1}function r0(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function r1(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function r2(t){t.resolveTargetDelta()}function r5(t){t.calcProjection()}function r3(t){t.resetSkewAndRotation()}function r6(t){t.removeLeadSnapshot()}function r4(t,e,i){t.translate=tE(e.translate,0,i),t.scale=tE(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function r9(t,e,i,r){t.min=tE(e.min,i.min,r),t.max=tE(e.max,i.max,r)}function r7(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let r8={duration:.45,ease:[.4,0,.1,1]},st=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),se=st("applewebkit/")&&!st("chrome/")?Math.round:m;function si(t){t.min=se(t.min),t.max=se(t.max)}function sr(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(rO(e)-rO(i)))}function ss(t){return t!==t.root&&t.scroll?.wasRoot}let sn=rY({attachResizeListener:(t,e)=>ix(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),so={current:void 0},sa=rY({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!so.current){let t=new sn({});t.mount(window),t.setOptions({layoutScroll:!0}),so.current=t}return so.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function sl(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),r=new AbortController;return[i,{passive:!0,...e,signal:r.signal},()=>r.abort()]}function su(t){return!("touch"===t.pointerType||iv.x||iv.y)}function sh(t,e,i){let{props:r}=t;t.animationState&&r.whileHover&&t.animationState.setActive("whileHover","Start"===i);let s=r["onHover"+i];s&&v.postRender(()=>s(e,iw(e)))}class sd extends im{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,s,n]=sl(t,i),o=t=>{if(!su(t))return;let{target:i}=t,r=e(i,t);if("function"!=typeof r||!i)return;let n=t=>{su(t)&&(r(t),i.removeEventListener("pointerleave",n))};i.addEventListener("pointerleave",n,s)};return r.forEach(t=>{t.addEventListener("pointerenter",o,s)}),n}(t,(t,e)=>(sh(this.node,e,"Start"),t=>sh(this.node,t,"End"))))}unmount(){}}class sc extends im{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=I(ix(this.node.current,"focus",()=>this.onFocus()),ix(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let sm=(t,e)=>!!e&&(t===e||sm(t,e.parentElement)),sp=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),sf=new WeakSet;function sg(t){return e=>{"Enter"===e.key&&t(e)}}function sy(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let sv=(t,e)=>{let i=t.currentTarget;if(!i)return;let r=sg(()=>{if(sf.has(i))return;sy(i,"down");let t=sg(()=>{sy(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>sy(i,"cancel"),e)});i.addEventListener("keydown",r,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),e)};function sx(t){return ib(t)&&!(iv.x||iv.y)}function sb(t,e,i){let{props:r}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&r.whileTap&&t.animationState.setActive("whileTap","Start"===i);let s=r["onTap"+("End"===i?"":i)];s&&v.postRender(()=>s(e,iw(e)))}class sw extends im{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,s,n]=sl(t,i),o=t=>{let r=t.currentTarget;if(!sx(t))return;sf.add(r);let n=e(r,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),sf.has(r)&&sf.delete(r),sx(t)&&"function"==typeof n&&n(t,{success:e})},a=t=>{o(t,r===window||r===document||i.useGlobalTarget||sm(r,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return r.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,s),eJ(t))&&(t.addEventListener("focus",t=>sv(t,s)),sp.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),n}(t,(t,e)=>(sb(this.node,e,"Start"),(t,{success:e})=>sb(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let sk=new WeakMap,sP=new WeakMap,sT=t=>{let e=sk.get(t.target);e&&e(t)},sS=t=>{t.forEach(sT)},sA={some:0,all:1};class sM extends im{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:r="some",once:s}=t,n={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:sA[r]};return function(t,e,i){let r=function({root:t,...e}){let i=t||document;sP.has(i)||sP.set(i,{});let r=sP.get(i),s=JSON.stringify(e);return r[s]||(r[s]=new IntersectionObserver(sS,{root:t,...e})),r[s]}(e);return sk.set(t,i),r.observe(t),()=>{sk.delete(t),r.unobserve(t)}}(this.node.current,n,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,s&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),n=e?i:r;n&&n(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let sj=(0,a.createContext)({strict:!1}),sC=(0,a.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),sE=(0,a.createContext)({});function sV(t){return l(t.animate)||ia.some(e=>is(t[e]))}function sD(t){return!!(sV(t)||t.variants)}function sR(t){return Array.isArray(t)?t.join(" "):t}let sL="undefined"!=typeof window,sN={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},sF={};for(let t in sN)sF[t]={isEnabled:e=>sN[t].some(t=>!!e[t])};let sB=Symbol.for("motionComponentSymbol"),sO=sL?a.useLayoutEffect:a.useEffect;function sz(t,{layout:e,layoutId:i}){return P.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!ru[t]||"opacity"===t)}let sI=(t,e)=>e&&"number"==typeof t?e.transform(t):t,sU={...Q,transform:Math.round},s$={borderWidth:tm,borderTopWidth:tm,borderRightWidth:tm,borderBottomWidth:tm,borderLeftWidth:tm,borderRadius:tm,radius:tm,borderTopLeftRadius:tm,borderTopRightRadius:tm,borderBottomRightRadius:tm,borderBottomLeftRadius:tm,width:tm,maxWidth:tm,height:tm,maxHeight:tm,top:tm,right:tm,bottom:tm,left:tm,padding:tm,paddingTop:tm,paddingRight:tm,paddingBottom:tm,paddingLeft:tm,margin:tm,marginTop:tm,marginRight:tm,marginBottom:tm,marginLeft:tm,backgroundPositionX:tm,backgroundPositionY:tm,rotate:td,rotateX:td,rotateY:td,rotateZ:td,scale:tt,scaleX:tt,scaleY:tt,scaleZ:tt,skew:td,skewX:td,skewY:td,distance:tm,translateX:tm,translateY:tm,translateZ:tm,x:tm,y:tm,z:tm,perspective:tm,transformPerspective:tm,opacity:J,originX:tg,originY:tg,originZ:tm,zIndex:sU,fillOpacity:J,strokeOpacity:J,numOctaves:sU},sW={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},sq=k.length;function sH(t,e,i){let{style:r,vars:s,transformOrigin:n}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(P.has(t)){o=!0;continue}if(G(t)){s[t]=i;continue}{let e=sI(i,s$[t]);t.startsWith("origin")?(a=!0,n[t]=e):r[t]=e}}if(!e.transform&&(o||i?r.transform=function(t,e,i){let r="",s=!0;for(let n=0;n<sq;n++){let o=k[n],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=sI(a,s$[o]);if(!l){s=!1;let e=sW[o]||o;r+=`${e}(${t}) `}i&&(e[o]=t)}}return r=r.trim(),i?r=i(e,s?"":r):s&&(r="none"),r}(e,t.transform,i):r.transform&&(r.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=n;r.transformOrigin=`${t} ${e} ${i}`}}let sY=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function sX(t,e,i){for(let r in e)N(e[r])||sz(r,i)||(t[r]=e[r])}let sG={offset:"stroke-dashoffset",array:"stroke-dasharray"},s_={offset:"strokeDashoffset",array:"strokeDasharray"};function sK(t,{attrX:e,attrY:i,attrScale:r,pathLength:s,pathSpacing:n=1,pathOffset:o=0,...a},l,u,h){if(sH(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=h?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==r&&(d.scale=r),void 0!==s&&function(t,e,i=1,r=0,s=!0){t.pathLength=1;let n=s?sG:s_;t[n.offset]=tm.transform(-r);let o=tm.transform(e),a=tm.transform(i);t[n.array]=`${o} ${a}`}(d,s,n,o,!1)}let sZ=()=>({...sY(),attrs:{}}),sQ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),sJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function s0(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||sJ.has(t)}let s1=t=>!s0(t);try{!function(t){t&&(s1=e=>e.startsWith("on")?!s0(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let s2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function s5(t){if("string"!=typeof t||t.includes("-"));else if(s2.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}function s3(t){let e=(0,a.useRef)(null);return null===e.current&&(e.current=t()),e.current}let s6=t=>(e,i)=>{let r=(0,a.useContext)(sE),s=(0,a.useContext)(ri),n=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,r,s){return{latestValues:function(t,e,i,r){let s={},n=r(t,{});for(let t in n)s[t]=rg(n[t]);let{initial:o,animate:a}=t,u=sV(t),d=sD(t);e&&d&&!u&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let c=!!i&&!1===i.initial,m=(c=c||!1===o)?a:o;if(m&&"boolean"!=typeof m&&!l(m)){let e=Array.isArray(m)?m:[m];for(let i=0;i<e.length;i++){let r=h(t,e[i]);if(r){let{transitionEnd:t,transition:e,...i}=r;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(i,r,s,t),renderState:e()}})(t,e,r,s);return i?n():s3(n)};function s4(t,e,i){let{style:r}=t,s={};for(let n in r)(N(r[n])||e.style&&N(e.style[n])||sz(n,t)||i?.getValue(n)?.liveStyle!==void 0)&&(s[n]=r[n]);return s}let s9={useVisualState:s6({scrapeMotionValuesFromProps:s4,createRenderState:sY})};function s7(t,e,i){let r=s4(t,e,i);for(let i in t)(N(t[i])||N(e[i]))&&(r[-1!==k.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}let s8={useVisualState:s6({scrapeMotionValuesFromProps:s7,createRenderState:sZ})},nt=t=>e=>e.test(t),ne=[Q,tm,tc,td,tf,tp,{test:t=>"auto"===t,parse:t=>t}],ni=t=>ne.find(nt(t)),nr=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ns=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,nn=t=>/^0[^.\s]+$/u.test(t),no=new Set(["brightness","contrast","saturate","opacity"]);function na(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[r]=i.match(ti)||[];if(!r)return t;let s=i.replace(r,""),n=+!!no.has(e);return r!==i&&(n*=100),e+"("+n+s+")"}let nl=/\b([a-z-]*)\(.*?\)/gu,nu={...tM,getAnimatableNone:t=>{let e=t.match(nl);return e?e.map(na).join(" "):t}},nh={...s$,color:tv,backgroundColor:tv,outlineColor:tv,fill:tv,stroke:tv,borderColor:tv,borderTopColor:tv,borderRightColor:tv,borderBottomColor:tv,borderLeftColor:tv,filter:nu,WebkitFilter:nu},nd=t=>nh[t];function nc(t,e){let i=nd(t);return i!==nu&&(i=tM),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let nm=new Set(["auto","none","0"]);class np extends ez{constructor(t,e,i,r,s){super(t,e,i,r,s,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let r=t[i];if("string"==typeof r&&K(r=r.trim())){let s=function t(e,i,r=1){Y(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[s,n]=function(t){let e=ns.exec(t);if(!e)return[,];let[,i,r,s]=e;return[`--${i??r}`,s]}(e);if(!s)return;let o=window.getComputedStyle(i).getPropertyValue(s);if(o){let t=o.trim();return nr(t)?parseFloat(t):t}return K(n)?t(n,i,r+1):n}(r,e.current);void 0!==s&&(t[i]=s),i===t.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!T.has(i)||2!==t.length)return;let[r,s]=t,n=ni(r),o=ni(s);if(n!==o)if(eC(n)&&eC(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else eD[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var r;(null===t[e]||("number"==typeof(r=t[e])?0===r:null===r||"none"===r||"0"===r||nn(r)))&&i.push(e)}i.length&&function(t,e,i){let r,s=0;for(;s<t.length&&!r;){let e=t[s];"string"==typeof e&&!nm.has(e)&&tP(e).values.length&&(r=t[s]),s++}if(r&&i)for(let s of e)t[s]=nc(i,r)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eD[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let r=e[e.length-1];void 0!==r&&t.getValue(i,r).jump(r,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let r=t.getValue(e);r&&r.jump(this.measuredOrigin,!1);let s=i.length-1,n=i[s];i[s]=eD[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==n&&void 0===this.finalKeyframe&&(this.finalKeyframe=n),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let nf=[...ne,tv,tM],ng=t=>nf.find(nt(t)),ny={current:null},nv={current:!1},nx=new WeakMap,nb=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class nw{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:s,visualState:n},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ez,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=C.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,v.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=n;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=o,this.blockInitialAnimation=!!s,this.isControllingVariants=sV(e),this.isVariantNode=sD(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&N(e)&&e.set(a[t],!1)}}mount(t){this.current=t,nx.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),nv.current||function(){if(nv.current=!0,sL)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ny.current=t.matches;t.addListener(e),e()}else ny.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ny.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),x(this.notifyUpdate),x(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let r=P.has(t);r&&this.onBindTransform&&this.onBindTransform();let s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&v.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),n=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),n(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in sF){let e=sF[t];if(!e)continue;let{isEnabled:i,Feature:r}=e;if(!this.features[t]&&r&&i(this.props)&&(this.features[t]=new r(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iL()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<nb.length;e++){let i=nb[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=t["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(t,e,i){for(let r in e){let s=e[r],n=i[r];if(N(s))t.addValue(r,s);else if(N(n))t.addValue(r,R(s,{owner:t}));else if(n!==s)if(t.hasValue(r)){let e=t.getValue(r);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(r);t.addValue(r,R(void 0!==e?e:s,{owner:t}))}}for(let r in i)void 0===e[r]&&t.removeValue(r);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=R(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(nr(i)||nn(i))?i=parseFloat(i):!ng(i)&&tM.test(e)&&(i=nc(t,e)),this.setBaseTarget(t,N(i)?i.get():i)),N(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let r=h(this.props,i,this.presenceContext?.custom);r&&(e=r[t])}if(i&&void 0!==e)return e;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||N(r)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new M),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class nk extends nw{constructor(){super(...arguments),this.KeyframeResolver=np}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;N(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function nP(t,{style:e,vars:i},r,s){for(let n in Object.assign(t.style,e,s&&s.getProjectionStyles(r)),i)t.style.setProperty(n,i[n])}class nT extends nk{constructor(){super(...arguments),this.type="html",this.renderInstance=nP}readValueFromInstance(t,e){if(P.has(e))return this.projection?.isProjecting?eS(e):eM(t,e);{let i=window.getComputedStyle(t),r=(G(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return iY(t,e)}build(t,e,i){sH(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return s4(t,e,i)}}let nS=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class nA extends nk{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iL}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(P.has(e)){let t=nd(e);return t&&t.default||0}return e=nS.has(e)?e:B(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return s7(t,e,i)}build(t,e,i){sK(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,r){for(let i in nP(t,e,void 0,r),e.attrs)t.setAttribute(nS.has(i)?i:B(i),e.attrs[i])}mount(t){this.isSVGTag=sQ(t.tagName),super.mount(t)}}let nM=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,r)=>"create"===r?t:(e.has(r)||e.set(r,t(r)),e.get(r))})}((s={animation:{Feature:ip},exit:{Feature:iy},inView:{Feature:sM},tap:{Feature:sw},focus:{Feature:sc},hover:{Feature:sd},pan:{Feature:rt},drag:{Feature:i7,ProjectionNode:sa,MeasureLayout:rd},layout:{ProjectionNode:sa,MeasureLayout:rd}},n=(t,e)=>s5(t)?new nA(e):new nT(e,{allowProjection:t!==a.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:r,Component:s}){function n(t,n){var l,u,h;let d,c={...(0,a.useContext)(sC),...t,layoutId:function({layoutId:t}){let e=(0,a.useContext)(rs).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:m}=c,p=function(t){let{initial:e,animate:i}=function(t,e){if(sV(t)){let{initial:e,animate:i}=t;return{initial:!1===e||is(e)?e:void 0,animate:is(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,a.useContext)(sE));return(0,a.useMemo)(()=>({initial:e,animate:i}),[sR(e),sR(i)])}(t),f=r(t,m);if(!m&&sL){u=0,h=0,(0,a.useContext)(sj).strict;let t=function(t){let{drag:e,layout:i}=sF;if(!e&&!i)return{};let r={...e,...i};return{MeasureLayout:e?.isEnabled(t)||i?.isEnabled(t)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(c);d=t.MeasureLayout,p.visualElement=function(t,e,i,r,s){let{visualElement:n}=(0,a.useContext)(sE),o=(0,a.useContext)(sj),l=(0,a.useContext)(ri),u=(0,a.useContext)(sC).reducedMotion,h=(0,a.useRef)(null);r=r||o.renderer,!h.current&&r&&(h.current=r(t,{visualState:e,parent:n,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));let d=h.current,c=(0,a.useContext)(rn);d&&!d.projection&&s&&("html"===d.type||"svg"===d.type)&&function(t,e,i,r){let{layoutId:s,layout:n,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:s,layout:n,alwaysMeasureLayout:!!o||a&&iG(a),visualElement:t,animationType:"string"==typeof n?n:"both",initialPromotionConfig:r,crossfade:h,layoutScroll:l,layoutRoot:u})}(h.current,i,s,c);let m=(0,a.useRef)(!1);(0,a.useInsertionEffect)(()=>{d&&m.current&&d.update(i,l)});let p=i[O],f=(0,a.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return sO(()=>{d&&(m.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),re.render(d.render),f.current&&d.animationState&&d.animationState.animateChanges())}),(0,a.useEffect)(()=>{d&&(!f.current&&d.animationState&&d.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),f.current=!1))}),d}(s,f,c,e,t.ProjectionNode)}return(0,o.jsxs)(sE.Provider,{value:p,children:[d&&p.visualElement?(0,o.jsx)(d,{visualElement:p.visualElement,...c}):null,i(s,t,(l=p.visualElement,(0,a.useCallback)(t=>{t&&f.onMount&&f.onMount(t),l&&(t?l.mount(t):l.unmount()),n&&("function"==typeof n?n(t):iG(n)&&(n.current=t))},[l])),f,m,p.visualElement)]})}t&&function(t){for(let e in t)sF[e]={...sF[e],...t[e]}}(t),n.displayName=`motion.${"string"==typeof s?s:`create(${s.displayName??s.name??""})`}`;let l=(0,a.forwardRef)(n);return l[sB]=s,l}({...s5(t)?s8:s9,preloadedFeatures:s,useRender:function(t=!1){return(e,i,r,{latestValues:s},n)=>{let o=(s5(e)?function(t,e,i,r){let s=(0,a.useMemo)(()=>{let i=sZ();return sK(i,e,sQ(r),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};sX(e,t.style,t),s.style={...e,...s.style}}return s}:function(t,e){let i={},r=function(t,e){let i=t.style||{},r={};return sX(r,i,t),Object.assign(r,function({transformTemplate:t},e){return(0,a.useMemo)(()=>{let i=sY();return sH(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),r}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=r,i})(i,s,n,e),l=function(t,e,i){let r={};for(let s in t)("values"!==s||"object"!=typeof t.values)&&(s1(s)||!0===i&&s0(s)||!e&&!s0(s)||t.draggable&&s.startsWith("onDrag"))&&(r[s]=t[s]);return r}(i,"string"==typeof e,t),u=e!==a.Fragment?{...l,...o,ref:r}:{},{children:h}=i,d=(0,a.useMemo)(()=>N(h)?h.get():h,[h]);return(0,a.createElement)(e,{...u,children:d})}}(e),createVisualElement:n,Component:t})}));class nj extends a.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,i=eJ(t)&&t.offsetWidth||0,r=this.props.sizeRef.current;r.height=e.offsetHeight||0,r.width=e.offsetWidth||0,r.top=e.offsetTop,r.left=e.offsetLeft,r.right=i-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function nC({children:t,isPresent:e,anchorX:i}){let r=(0,a.useId)(),s=(0,a.useRef)(null),n=(0,a.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,a.useContext)(sC);return(0,a.useInsertionEffect)(()=>{let{width:t,height:o,top:a,left:u,right:h}=n.current;if(e||!s.current||!t||!o)return;let d="left"===i?`left: ${u}`:`right: ${h}`;s.current.dataset.motionPopId=r;let c=document.createElement("style");return l&&(c.nonce=l),document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${t}px !important;
            height: ${o}px !important;
            ${d}px !important;
            top: ${a}px !important;
          }
        `),()=>{document.head.contains(c)&&document.head.removeChild(c)}},[e]),(0,o.jsx)(nj,{isPresent:e,childRef:s,sizeRef:n,children:a.cloneElement(t,{ref:s})})}let nE=({children:t,initial:e,isPresent:i,onExitComplete:r,custom:s,presenceAffectsLayout:n,mode:l,anchorX:u})=>{let h=s3(nV),d=(0,a.useId)(),c=!0,m=(0,a.useMemo)(()=>(c=!1,{id:d,initial:e,isPresent:i,custom:s,onExitComplete:t=>{for(let e of(h.set(t,!0),h.values()))if(!e)return;r&&r()},register:t=>(h.set(t,!1),()=>h.delete(t))}),[i,h,r]);return n&&c&&(m={...m}),(0,a.useMemo)(()=>{h.forEach((t,e)=>h.set(e,!1))},[i]),a.useEffect(()=>{i||h.size||!r||r()},[i]),"popLayout"===l&&(t=(0,o.jsx)(nC,{isPresent:i,anchorX:u,children:t})),(0,o.jsx)(ri.Provider,{value:m,children:t})};function nV(){return new Map}let nD=t=>t.key||"";function nR(t){let e=[];return a.Children.forEach(t,t=>{(0,a.isValidElement)(t)&&e.push(t)}),e}let nL=({children:t,custom:e,initial:i=!0,onExitComplete:r,presenceAffectsLayout:s=!0,mode:n="sync",propagate:l=!1,anchorX:u="left"})=>{let[h,d]=rr(l),c=(0,a.useMemo)(()=>nR(t),[t]),m=l&&!h?[]:c.map(nD),p=(0,a.useRef)(!0),f=(0,a.useRef)(c),g=s3(()=>new Map),[y,v]=(0,a.useState)(c),[x,b]=(0,a.useState)(c);sO(()=>{p.current=!1,f.current=c;for(let t=0;t<x.length;t++){let e=nD(x[t]);m.includes(e)?g.delete(e):!0!==g.get(e)&&g.set(e,!1)}},[x,m.length,m.join("-")]);let w=[];if(c!==y){let t=[...c];for(let e=0;e<x.length;e++){let i=x[e],r=nD(i);m.includes(r)||(t.splice(e,0,i),w.push(i))}return"wait"===n&&w.length&&(t=w),b(nR(t)),v(c),null}let{forceRender:k}=(0,a.useContext)(rs);return(0,o.jsx)(o.Fragment,{children:x.map(t=>{let a=nD(t),y=(!l||!!h)&&(c===x||m.includes(a));return(0,o.jsx)(nE,{isPresent:y,initial:(!p.current||!!i)&&void 0,custom:e,presenceAffectsLayout:s,mode:n,onExitComplete:y?void 0:()=>{if(!g.has(a))return;g.set(a,!0);let t=!0;g.forEach(e=>{e||(t=!1)}),t&&(k?.(),b(f.current),l&&d?.(),r&&r())},anchorX:u,children:t},a)})})},nN=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),nF=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,i)=>i?i.toUpperCase():e.toLowerCase()),nB=t=>{let e=nF(t);return e.charAt(0).toUpperCase()+e.slice(1)},nO=(...t)=>t.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim(),nz=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};var nI={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let nU=(0,a.forwardRef)(({color:t="currentColor",size:e=24,strokeWidth:i=2,absoluteStrokeWidth:r,className:s="",children:n,iconNode:o,...l},u)=>(0,a.createElement)("svg",{ref:u,...nI,width:e,height:e,stroke:t,strokeWidth:r?24*Number(i)/Number(e):i,className:nO("lucide",s),...!n&&!nz(l)&&{"aria-hidden":"true"},...l},[...o.map(([t,e])=>(0,a.createElement)(t,e)),...Array.isArray(n)?n:[n]])),n$=(t,e)=>{let i=(0,a.forwardRef)(({className:i,...r},s)=>(0,a.createElement)(nU,{ref:s,iconNode:e,className:nO(`lucide-${nN(nB(t))}`,`lucide-${t}`,i),...r}));return i.displayName=nB(t),i},nW=n$("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]),nq=n$("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),nH=n$("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),nY=t=>{let e=nK(t),{conflictingClassGroups:i,conflictingClassGroupModifiers:r}=t;return{getClassGroupId:t=>{let i=t.split("-");return""===i[0]&&1!==i.length&&i.shift(),nX(i,e)||n_(t)},getConflictingClassGroupIds:(t,e)=>{let s=i[t]||[];return e&&r[t]?[...s,...r[t]]:s}}},nX=(t,e)=>{if(0===t.length)return e.classGroupId;let i=t[0],r=e.nextPart.get(i),s=r?nX(t.slice(1),r):void 0;if(s)return s;if(0===e.validators.length)return;let n=t.join("-");return e.validators.find(({validator:t})=>t(n))?.classGroupId},nG=/^\[(.+)\]$/,n_=t=>{if(nG.test(t)){let e=nG.exec(t)[1],i=e?.substring(0,e.indexOf(":"));if(i)return"arbitrary.."+i}},nK=t=>{let{theme:e,classGroups:i}=t,r={nextPart:new Map,validators:[]};for(let t in i)nZ(i[t],r,t,e);return r},nZ=(t,e,i,r)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:nQ(e,t)).classGroupId=i;return}if("function"==typeof t)return nJ(t)?void nZ(t(r),e,i,r):void e.validators.push({validator:t,classGroupId:i});Object.entries(t).forEach(([t,s])=>{nZ(s,nQ(e,t),i,r)})})},nQ=(t,e)=>{let i=t;return e.split("-").forEach(t=>{i.nextPart.has(t)||i.nextPart.set(t,{nextPart:new Map,validators:[]}),i=i.nextPart.get(t)}),i},nJ=t=>t.isThemeGetter,n0=t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,i=new Map,r=new Map,s=(s,n)=>{i.set(s,n),++e>t&&(e=0,r=i,i=new Map)};return{get(t){let e=i.get(t);return void 0!==e?e:void 0!==(e=r.get(t))?(s(t,e),e):void 0},set(t,e){i.has(t)?i.set(t,e):s(t,e)}}},n1=t=>{let{prefix:e,experimentalParseClassName:i}=t,r=t=>{let e,i=[],r=0,s=0,n=0;for(let o=0;o<t.length;o++){let a=t[o];if(0===r&&0===s){if(":"===a){i.push(t.slice(n,o)),n=o+1;continue}if("/"===a){e=o;continue}}"["===a?r++:"]"===a?r--:"("===a?s++:")"===a&&s--}let o=0===i.length?t:t.substring(n),a=n2(o);return{modifiers:i,hasImportantModifier:a!==o,baseClassName:a,maybePostfixModifierPosition:e&&e>n?e-n:void 0}};if(e){let t=e+":",i=r;r=e=>e.startsWith(t)?i(e.substring(t.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:e,maybePostfixModifierPosition:void 0}}if(i){let t=r;r=e=>i({className:e,parseClassName:t})}return r},n2=t=>t.endsWith("!")?t.substring(0,t.length-1):t.startsWith("!")?t.substring(1):t,n5=t=>{let e=Object.fromEntries(t.orderSensitiveModifiers.map(t=>[t,!0]));return t=>{if(t.length<=1)return t;let i=[],r=[];return t.forEach(t=>{"["===t[0]||e[t]?(i.push(...r.sort(),t),r=[]):r.push(t)}),i.push(...r.sort()),i}},n3=t=>({cache:n0(t.cacheSize),parseClassName:n1(t),sortModifiers:n5(t),...nY(t)}),n6=/\s+/,n4=(t,e)=>{let{parseClassName:i,getClassGroupId:r,getConflictingClassGroupIds:s,sortModifiers:n}=e,o=[],a=t.trim().split(n6),l="";for(let t=a.length-1;t>=0;t-=1){let e=a[t],{isExternal:u,modifiers:h,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:m}=i(e);if(u){l=e+(l.length>0?" "+l:l);continue}let p=!!m,f=r(p?c.substring(0,m):c);if(!f){if(!p||!(f=r(c))){l=e+(l.length>0?" "+l:l);continue}p=!1}let g=n(h).join(":"),y=d?g+"!":g,v=y+f;if(o.includes(v))continue;o.push(v);let x=s(f,p);for(let t=0;t<x.length;++t){let e=x[t];o.push(y+e)}l=e+(l.length>0?" "+l:l)}return l};function n9(){let t,e,i=0,r="";for(;i<arguments.length;)(t=arguments[i++])&&(e=n7(t))&&(r&&(r+=" "),r+=e);return r}let n7=t=>{let e;if("string"==typeof t)return t;let i="";for(let r=0;r<t.length;r++)t[r]&&(e=n7(t[r]))&&(i&&(i+=" "),i+=e);return i},n8=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},ot=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,oe=/^\((?:(\w[\w-]*):)?(.+)\)$/i,oi=/^\d+\/\d+$/,or=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,os=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,on=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,oo=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,oa=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,ol=t=>oi.test(t),ou=t=>!!t&&!Number.isNaN(Number(t)),oh=t=>!!t&&Number.isInteger(Number(t)),od=t=>t.endsWith("%")&&ou(t.slice(0,-1)),oc=t=>or.test(t),om=()=>!0,op=t=>os.test(t)&&!on.test(t),of=()=>!1,og=t=>oo.test(t),oy=t=>oa.test(t),ov=t=>!ob(t)&&!oA(t),ox=t=>oR(t,oB,of),ob=t=>ot.test(t),ow=t=>oR(t,oO,op),ok=t=>oR(t,oz,ou),oP=t=>oR(t,oN,of),oT=t=>oR(t,oF,oy),oS=t=>oR(t,oU,og),oA=t=>oe.test(t),oM=t=>oL(t,oO),oj=t=>oL(t,oI),oC=t=>oL(t,oN),oE=t=>oL(t,oB),oV=t=>oL(t,oF),oD=t=>oL(t,oU,!0),oR=(t,e,i)=>{let r=ot.exec(t);return!!r&&(r[1]?e(r[1]):i(r[2]))},oL=(t,e,i=!1)=>{let r=oe.exec(t);return!!r&&(r[1]?e(r[1]):i)},oN=t=>"position"===t||"percentage"===t,oF=t=>"image"===t||"url"===t,oB=t=>"length"===t||"size"===t||"bg-size"===t,oO=t=>"length"===t,oz=t=>"number"===t,oI=t=>"family-name"===t,oU=t=>"shadow"===t;Symbol.toStringTag;let o$=function(t,...e){let i,r,s,n=function(a){return r=(i=n3(e.reduce((t,e)=>e(t),t()))).cache.get,s=i.cache.set,n=o,o(a)};function o(t){let e=r(t);if(e)return e;let n=n4(t,i);return s(t,n),n}return function(){return n(n9.apply(null,arguments))}}(()=>{let t=n8("color"),e=n8("font"),i=n8("text"),r=n8("font-weight"),s=n8("tracking"),n=n8("leading"),o=n8("breakpoint"),a=n8("container"),l=n8("spacing"),u=n8("radius"),h=n8("shadow"),d=n8("inset-shadow"),c=n8("text-shadow"),m=n8("drop-shadow"),p=n8("blur"),f=n8("perspective"),g=n8("aspect"),y=n8("ease"),v=n8("animate"),x=()=>["auto","avoid","all","avoid-page","page","left","right","column"],b=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...b(),oA,ob],k=()=>["auto","hidden","clip","visible","scroll"],P=()=>["auto","contain","none"],T=()=>[oA,ob,l],S=()=>[ol,"full","auto",...T()],A=()=>[oh,"none","subgrid",oA,ob],M=()=>["auto",{span:["full",oh,oA,ob]},oh,oA,ob],j=()=>[oh,"auto",oA,ob],C=()=>["auto","min","max","fr",oA,ob],E=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],V=()=>["start","end","center","stretch","center-safe","end-safe"],D=()=>["auto",...T()],R=()=>[ol,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...T()],L=()=>[t,oA,ob],N=()=>[...b(),oC,oP,{position:[oA,ob]}],F=()=>["no-repeat",{repeat:["","x","y","space","round"]}],B=()=>["auto","cover","contain",oE,ox,{size:[oA,ob]}],O=()=>[od,oM,ow],z=()=>["","none","full",u,oA,ob],I=()=>["",ou,oM,ow],U=()=>["solid","dashed","dotted","double"],$=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],W=()=>[ou,od,oC,oP],q=()=>["","none",p,oA,ob],H=()=>["none",ou,oA,ob],Y=()=>["none",ou,oA,ob],X=()=>[ou,oA,ob],G=()=>[ol,"full",...T()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[oc],breakpoint:[oc],color:[om],container:[oc],"drop-shadow":[oc],ease:["in","out","in-out"],font:[ov],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[oc],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[oc],shadow:[oc],spacing:["px",ou],text:[oc],"text-shadow":[oc],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",ol,ob,oA,g]}],container:["container"],columns:[{columns:[ou,ob,oA,a]}],"break-after":[{"break-after":x()}],"break-before":[{"break-before":x()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:k()}],"overflow-x":[{"overflow-x":k()}],"overflow-y":[{"overflow-y":k()}],overscroll:[{overscroll:P()}],"overscroll-x":[{"overscroll-x":P()}],"overscroll-y":[{"overscroll-y":P()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:S()}],"inset-x":[{"inset-x":S()}],"inset-y":[{"inset-y":S()}],start:[{start:S()}],end:[{end:S()}],top:[{top:S()}],right:[{right:S()}],bottom:[{bottom:S()}],left:[{left:S()}],visibility:["visible","invisible","collapse"],z:[{z:[oh,"auto",oA,ob]}],basis:[{basis:[ol,"full","auto",a,...T()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[ou,ol,"auto","initial","none",ob]}],grow:[{grow:["",ou,oA,ob]}],shrink:[{shrink:["",ou,oA,ob]}],order:[{order:[oh,"first","last","none",oA,ob]}],"grid-cols":[{"grid-cols":A()}],"col-start-end":[{col:M()}],"col-start":[{"col-start":j()}],"col-end":[{"col-end":j()}],"grid-rows":[{"grid-rows":A()}],"row-start-end":[{row:M()}],"row-start":[{"row-start":j()}],"row-end":[{"row-end":j()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":C()}],"auto-rows":[{"auto-rows":C()}],gap:[{gap:T()}],"gap-x":[{"gap-x":T()}],"gap-y":[{"gap-y":T()}],"justify-content":[{justify:[...E(),"normal"]}],"justify-items":[{"justify-items":[...V(),"normal"]}],"justify-self":[{"justify-self":["auto",...V()]}],"align-content":[{content:["normal",...E()]}],"align-items":[{items:[...V(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...V(),{baseline:["","last"]}]}],"place-content":[{"place-content":E()}],"place-items":[{"place-items":[...V(),"baseline"]}],"place-self":[{"place-self":["auto",...V()]}],p:[{p:T()}],px:[{px:T()}],py:[{py:T()}],ps:[{ps:T()}],pe:[{pe:T()}],pt:[{pt:T()}],pr:[{pr:T()}],pb:[{pb:T()}],pl:[{pl:T()}],m:[{m:D()}],mx:[{mx:D()}],my:[{my:D()}],ms:[{ms:D()}],me:[{me:D()}],mt:[{mt:D()}],mr:[{mr:D()}],mb:[{mb:D()}],ml:[{ml:D()}],"space-x":[{"space-x":T()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":T()}],"space-y-reverse":["space-y-reverse"],size:[{size:R()}],w:[{w:[a,"screen",...R()]}],"min-w":[{"min-w":[a,"screen","none",...R()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[o]},...R()]}],h:[{h:["screen","lh",...R()]}],"min-h":[{"min-h":["screen","lh","none",...R()]}],"max-h":[{"max-h":["screen","lh",...R()]}],"font-size":[{text:["base",i,oM,ow]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,oA,ok]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",od,ob]}],"font-family":[{font:[oj,ob,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,oA,ob]}],"line-clamp":[{"line-clamp":[ou,"none",oA,ok]}],leading:[{leading:[n,...T()]}],"list-image":[{"list-image":["none",oA,ob]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",oA,ob]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:L()}],"text-color":[{text:L()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...U(),"wavy"]}],"text-decoration-thickness":[{decoration:[ou,"from-font","auto",oA,ow]}],"text-decoration-color":[{decoration:L()}],"underline-offset":[{"underline-offset":[ou,"auto",oA,ob]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:T()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",oA,ob]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",oA,ob]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:N()}],"bg-repeat":[{bg:F()}],"bg-size":[{bg:B()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},oh,oA,ob],radial:["",oA,ob],conic:[oh,oA,ob]},oV,oT]}],"bg-color":[{bg:L()}],"gradient-from-pos":[{from:O()}],"gradient-via-pos":[{via:O()}],"gradient-to-pos":[{to:O()}],"gradient-from":[{from:L()}],"gradient-via":[{via:L()}],"gradient-to":[{to:L()}],rounded:[{rounded:z()}],"rounded-s":[{"rounded-s":z()}],"rounded-e":[{"rounded-e":z()}],"rounded-t":[{"rounded-t":z()}],"rounded-r":[{"rounded-r":z()}],"rounded-b":[{"rounded-b":z()}],"rounded-l":[{"rounded-l":z()}],"rounded-ss":[{"rounded-ss":z()}],"rounded-se":[{"rounded-se":z()}],"rounded-ee":[{"rounded-ee":z()}],"rounded-es":[{"rounded-es":z()}],"rounded-tl":[{"rounded-tl":z()}],"rounded-tr":[{"rounded-tr":z()}],"rounded-br":[{"rounded-br":z()}],"rounded-bl":[{"rounded-bl":z()}],"border-w":[{border:I()}],"border-w-x":[{"border-x":I()}],"border-w-y":[{"border-y":I()}],"border-w-s":[{"border-s":I()}],"border-w-e":[{"border-e":I()}],"border-w-t":[{"border-t":I()}],"border-w-r":[{"border-r":I()}],"border-w-b":[{"border-b":I()}],"border-w-l":[{"border-l":I()}],"divide-x":[{"divide-x":I()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":I()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...U(),"hidden","none"]}],"divide-style":[{divide:[...U(),"hidden","none"]}],"border-color":[{border:L()}],"border-color-x":[{"border-x":L()}],"border-color-y":[{"border-y":L()}],"border-color-s":[{"border-s":L()}],"border-color-e":[{"border-e":L()}],"border-color-t":[{"border-t":L()}],"border-color-r":[{"border-r":L()}],"border-color-b":[{"border-b":L()}],"border-color-l":[{"border-l":L()}],"divide-color":[{divide:L()}],"outline-style":[{outline:[...U(),"none","hidden"]}],"outline-offset":[{"outline-offset":[ou,oA,ob]}],"outline-w":[{outline:["",ou,oM,ow]}],"outline-color":[{outline:L()}],shadow:[{shadow:["","none",h,oD,oS]}],"shadow-color":[{shadow:L()}],"inset-shadow":[{"inset-shadow":["none",d,oD,oS]}],"inset-shadow-color":[{"inset-shadow":L()}],"ring-w":[{ring:I()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:L()}],"ring-offset-w":[{"ring-offset":[ou,ow]}],"ring-offset-color":[{"ring-offset":L()}],"inset-ring-w":[{"inset-ring":I()}],"inset-ring-color":[{"inset-ring":L()}],"text-shadow":[{"text-shadow":["none",c,oD,oS]}],"text-shadow-color":[{"text-shadow":L()}],opacity:[{opacity:[ou,oA,ob]}],"mix-blend":[{"mix-blend":[...$(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":$()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[ou]}],"mask-image-linear-from-pos":[{"mask-linear-from":W()}],"mask-image-linear-to-pos":[{"mask-linear-to":W()}],"mask-image-linear-from-color":[{"mask-linear-from":L()}],"mask-image-linear-to-color":[{"mask-linear-to":L()}],"mask-image-t-from-pos":[{"mask-t-from":W()}],"mask-image-t-to-pos":[{"mask-t-to":W()}],"mask-image-t-from-color":[{"mask-t-from":L()}],"mask-image-t-to-color":[{"mask-t-to":L()}],"mask-image-r-from-pos":[{"mask-r-from":W()}],"mask-image-r-to-pos":[{"mask-r-to":W()}],"mask-image-r-from-color":[{"mask-r-from":L()}],"mask-image-r-to-color":[{"mask-r-to":L()}],"mask-image-b-from-pos":[{"mask-b-from":W()}],"mask-image-b-to-pos":[{"mask-b-to":W()}],"mask-image-b-from-color":[{"mask-b-from":L()}],"mask-image-b-to-color":[{"mask-b-to":L()}],"mask-image-l-from-pos":[{"mask-l-from":W()}],"mask-image-l-to-pos":[{"mask-l-to":W()}],"mask-image-l-from-color":[{"mask-l-from":L()}],"mask-image-l-to-color":[{"mask-l-to":L()}],"mask-image-x-from-pos":[{"mask-x-from":W()}],"mask-image-x-to-pos":[{"mask-x-to":W()}],"mask-image-x-from-color":[{"mask-x-from":L()}],"mask-image-x-to-color":[{"mask-x-to":L()}],"mask-image-y-from-pos":[{"mask-y-from":W()}],"mask-image-y-to-pos":[{"mask-y-to":W()}],"mask-image-y-from-color":[{"mask-y-from":L()}],"mask-image-y-to-color":[{"mask-y-to":L()}],"mask-image-radial":[{"mask-radial":[oA,ob]}],"mask-image-radial-from-pos":[{"mask-radial-from":W()}],"mask-image-radial-to-pos":[{"mask-radial-to":W()}],"mask-image-radial-from-color":[{"mask-radial-from":L()}],"mask-image-radial-to-color":[{"mask-radial-to":L()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":b()}],"mask-image-conic-pos":[{"mask-conic":[ou]}],"mask-image-conic-from-pos":[{"mask-conic-from":W()}],"mask-image-conic-to-pos":[{"mask-conic-to":W()}],"mask-image-conic-from-color":[{"mask-conic-from":L()}],"mask-image-conic-to-color":[{"mask-conic-to":L()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:N()}],"mask-repeat":[{mask:F()}],"mask-size":[{mask:B()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",oA,ob]}],filter:[{filter:["","none",oA,ob]}],blur:[{blur:q()}],brightness:[{brightness:[ou,oA,ob]}],contrast:[{contrast:[ou,oA,ob]}],"drop-shadow":[{"drop-shadow":["","none",m,oD,oS]}],"drop-shadow-color":[{"drop-shadow":L()}],grayscale:[{grayscale:["",ou,oA,ob]}],"hue-rotate":[{"hue-rotate":[ou,oA,ob]}],invert:[{invert:["",ou,oA,ob]}],saturate:[{saturate:[ou,oA,ob]}],sepia:[{sepia:["",ou,oA,ob]}],"backdrop-filter":[{"backdrop-filter":["","none",oA,ob]}],"backdrop-blur":[{"backdrop-blur":q()}],"backdrop-brightness":[{"backdrop-brightness":[ou,oA,ob]}],"backdrop-contrast":[{"backdrop-contrast":[ou,oA,ob]}],"backdrop-grayscale":[{"backdrop-grayscale":["",ou,oA,ob]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[ou,oA,ob]}],"backdrop-invert":[{"backdrop-invert":["",ou,oA,ob]}],"backdrop-opacity":[{"backdrop-opacity":[ou,oA,ob]}],"backdrop-saturate":[{"backdrop-saturate":[ou,oA,ob]}],"backdrop-sepia":[{"backdrop-sepia":["",ou,oA,ob]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":T()}],"border-spacing-x":[{"border-spacing-x":T()}],"border-spacing-y":[{"border-spacing-y":T()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",oA,ob]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[ou,"initial",oA,ob]}],ease:[{ease:["linear","initial",y,oA,ob]}],delay:[{delay:[ou,oA,ob]}],animate:[{animate:["none",v,oA,ob]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,oA,ob]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:H()}],"rotate-x":[{"rotate-x":H()}],"rotate-y":[{"rotate-y":H()}],"rotate-z":[{"rotate-z":H()}],scale:[{scale:Y()}],"scale-x":[{"scale-x":Y()}],"scale-y":[{"scale-y":Y()}],"scale-z":[{"scale-z":Y()}],"scale-3d":["scale-3d"],skew:[{skew:X()}],"skew-x":[{"skew-x":X()}],"skew-y":[{"skew-y":X()}],transform:[{transform:[oA,ob,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:G()}],"translate-x":[{"translate-x":G()}],"translate-y":[{"translate-y":G()}],"translate-z":[{"translate-z":G()}],"translate-none":["translate-none"],accent:[{accent:L()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:L()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",oA,ob]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":T()}],"scroll-mx":[{"scroll-mx":T()}],"scroll-my":[{"scroll-my":T()}],"scroll-ms":[{"scroll-ms":T()}],"scroll-me":[{"scroll-me":T()}],"scroll-mt":[{"scroll-mt":T()}],"scroll-mr":[{"scroll-mr":T()}],"scroll-mb":[{"scroll-mb":T()}],"scroll-ml":[{"scroll-ml":T()}],"scroll-p":[{"scroll-p":T()}],"scroll-px":[{"scroll-px":T()}],"scroll-py":[{"scroll-py":T()}],"scroll-ps":[{"scroll-ps":T()}],"scroll-pe":[{"scroll-pe":T()}],"scroll-pt":[{"scroll-pt":T()}],"scroll-pr":[{"scroll-pr":T()}],"scroll-pb":[{"scroll-pb":T()}],"scroll-pl":[{"scroll-pl":T()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",oA,ob]}],fill:[{fill:["none",...L()]}],"stroke-w":[{stroke:[ou,oM,ow,ok]}],stroke:[{stroke:["none",...L()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function oW(...t){return o$(function(){for(var t,e,i=0,r="",s=arguments.length;i<s;i++)(t=arguments[i])&&(e=function t(e){var i,r,s="";if("string"==typeof e||"number"==typeof e)s+=e;else if("object"==typeof e)if(Array.isArray(e)){var n=e.length;for(i=0;i<n;i++)e[i]&&(r=t(e[i]))&&(s&&(s+=" "),s+=r)}else for(r in e)e[r]&&(s&&(s+=" "),s+=r);return s}(t))&&(r&&(r+=" "),r+=e);return r}(t))}let oq=(0,a.forwardRef)(({className:t,variant:e="default",size:i="default",...r},s)=>(0,o.jsx)("button",{className:oW("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-primary text-primary-foreground hover:bg-primary/90":"default"===e,"bg-destructive text-destructive-foreground hover:bg-destructive/90":"destructive"===e,"border border-input bg-background hover:bg-accent hover:text-accent-foreground":"outline"===e,"bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===e,"hover:bg-accent hover:text-accent-foreground":"ghost"===e,"text-primary underline-offset-4 hover:underline":"link"===e},{"h-10 px-4 py-2":"default"===i,"h-9 rounded-md px-3":"sm"===i,"h-11 rounded-md px-8":"lg"===i,"h-10 w-10":"icon"===i},t),ref:s,...r}));oq.displayName="Button";let oH=(0,a.forwardRef)(({className:t,...e},i)=>(0,o.jsx)("div",{ref:i,className:oW("rounded-lg border bg-card text-card-foreground shadow-sm",t),...e}));function oY({maxNumber:t=10,onComplete:e}){let[i,r]=(0,a.useState)(1),[s,n]=(0,a.useState)(0),[l,u]=(0,a.useState)(null),[h,d]=(0,a.useState)(null),[c,m]=(0,a.useState)(!1),[p,f]=(0,a.useState)(!1),g=t=>{let e=["\uD83C\uDF4E","\uD83C\uDF1F","\uD83C\uDF88","\uD83D\uDC31","\uD83D\uDE97","\uD83C\uDF38","\uD83C\uDF81","\uD83E\uDD8B"],i=e[Math.floor(Math.random()*e.length)];return Array(t).fill(i)},[y,v]=(0,a.useState)(g(i)),x=e=>{let i=[e];for(;i.length<4;){let e=Math.floor(Math.random()*t)+1;i.includes(e)||i.push(e)}return i.sort(()=>Math.random()-.5)},[b,w]=(0,a.useState)(x(i)),k=r=>{u(r);let o=r===i;d(o),o&&(n(s+10),f(!0),setTimeout(()=>f(!1),1e3)),setTimeout(()=>{i>=t?(m(!0),e?.(s+10*!!o)):P()},1500)},P=()=>{let t=i+1;r(t),v(g(t)),w(x(t)),u(null),d(null)};return c?(0,o.jsxs)(oH,{className:"p-8 text-center max-w-md mx-auto",children:[(0,o.jsxs)(nM.div,{initial:{scale:0},animate:{scale:1},className:"mb-6",children:[(0,o.jsx)(nW,{className:"h-16 w-16 text-yellow-500 mx-auto mb-4"}),(0,o.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Congratulations!"}),(0,o.jsx)("p",{className:"text-gray-600 mb-4",children:"You completed the counting game!"}),(0,o.jsx)("div",{className:"bg-gradient-to-r from-purple-100 to-blue-100 p-4 rounded-lg mb-6",children:(0,o.jsxs)("p",{className:"text-lg font-semibold text-purple-700",children:["Final Score: ",s," points"]})})]}),(0,o.jsxs)(oq,{onClick:()=>{r(1),n(0),u(null),d(null),m(!1),v(g(1)),w(x(1))},className:"w-full",children:[(0,o.jsx)(nq,{className:"mr-2 h-4 w-4"}),"Play Again"]})]}):(0,o.jsxs)(oH,{className:"p-6 max-w-2xl mx-auto",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(nH,{className:"h-6 w-6 text-yellow-500"}),(0,o.jsxs)("span",{className:"text-lg font-semibold",children:["Score: ",s]})]}),(0,o.jsxs)("div",{className:"text-sm text-gray-600",children:["Question ",i," of ",t]})]}),(0,o.jsxs)("div",{className:"text-center mb-8",children:[(0,o.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Count the objects below:"}),(0,o.jsx)("div",{className:"grid grid-cols-5 gap-4 justify-items-center mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg",children:(0,o.jsx)(nL,{children:y.map((t,e)=>(0,o.jsx)(nM.div,{initial:{scale:0,rotate:-180},animate:{scale:1,rotate:0},transition:{delay:.1*e},className:"text-4xl",children:t},e))})})]}),(0,o.jsx)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:b.map(t=>(0,o.jsx)(nM.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,o.jsx)(oq,{onClick:()=>k(t),disabled:null!==l,variant:l===t?h?"default":"destructive":"outline",className:"w-full h-16 text-2xl font-bold",children:t})},t))}),(0,o.jsx)(nL,{children:null!==h&&(0,o.jsx)(nM.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:`text-center p-4 rounded-lg ${h?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:h?"\uD83C\uDF89 Correct! Well done!":"❌ Try again next time!"})}),(0,o.jsx)(nL,{children:p&&(0,o.jsx)(nM.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 pointer-events-none flex items-center justify-center z-50",children:(0,o.jsx)(nM.div,{animate:{scale:[1,1.2,1],rotate:[0,360,0]},transition:{duration:1},className:"text-6xl",children:"⭐"})})})]})}oH.displayName="Card",(0,a.forwardRef)(({className:t,...e},i)=>(0,o.jsx)("div",{ref:i,className:oW("flex flex-col space-y-1.5 p-6",t),...e})).displayName="CardHeader",(0,a.forwardRef)(({className:t,...e},i)=>(0,o.jsx)("h3",{ref:i,className:oW("text-2xl font-semibold leading-none tracking-tight",t),...e})).displayName="CardTitle",(0,a.forwardRef)(({className:t,...e},i)=>(0,o.jsx)("p",{ref:i,className:oW("text-sm text-muted-foreground",t),...e})).displayName="CardDescription",(0,a.forwardRef)(({className:t,...e},i)=>(0,o.jsx)("div",{ref:i,className:oW("p-6 pt-0",t),...e})).displayName="CardContent",(0,a.forwardRef)(({className:t,...e},i)=>(0,o.jsx)("div",{ref:i,className:oW("flex items-center p-6 pt-0",t),...e})).displayName="CardFooter";var oX=i(5814),oG=i.n(oX);let o_=n$("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);function oK(){return(0,o.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-8",children:(0,o.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[(0,o.jsxs)("div",{className:"mb-8",children:[(0,o.jsx)(oG(),{href:"/",children:(0,o.jsxs)(oq,{variant:"ghost",className:"mb-4",children:[(0,o.jsx)(o_,{className:"mr-2 h-4 w-4"}),"Back to Home"]})}),(0,o.jsx)("h1",{className:"text-4xl font-bold text-center mb-4",children:(0,o.jsx)("span",{className:"bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent",children:"MathQuest Demo"})}),(0,o.jsx)("p",{className:"text-xl text-gray-600 text-center max-w-2xl mx-auto",children:"Try our interactive counting game designed for Grade 1 students. Count the objects and select the correct answer!"})]}),(0,o.jsx)("div",{className:"mb-8",children:(0,o.jsx)(oY,{maxNumber:5})}),(0,o.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 mt-12",children:[(0,o.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,o.jsx)("div",{className:"text-3xl mb-3",children:"\uD83C\uDFAF"}),(0,o.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Interactive Learning"}),(0,o.jsx)("p",{className:"text-gray-600",children:"Engaging games that make learning math fun and memorable for children."})]}),(0,o.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,o.jsx)("div",{className:"text-3xl mb-3",children:"\uD83D\uDCCA"}),(0,o.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Progress Tracking"}),(0,o.jsx)("p",{className:"text-gray-600",children:"Real-time feedback and detailed progress reports for students and parents."})]}),(0,o.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,o.jsx)("div",{className:"text-3xl mb-3",children:"\uD83C\uDFC6"}),(0,o.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Achievement System"}),(0,o.jsx)("p",{className:"text-gray-600",children:"Badges, points, and rewards that motivate students to keep learning."})]})]}),(0,o.jsxs)("div",{className:"text-center mt-12",children:[(0,o.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Ready to Start Learning?"}),(0,o.jsx)("p",{className:"text-gray-600 mb-6",children:"Join thousands of students already improving their math skills with MathQuest."}),(0,o.jsxs)("div",{className:"space-x-4",children:[(0,o.jsx)(oG(),{href:"/register",children:(0,o.jsx)(oq,{size:"lg",children:"Get Started Free"})}),(0,o.jsx)(oG(),{href:"/",children:(0,o.jsx)(oq,{variant:"outline",size:"lg",children:"Learn More"})})]})]})]})})}},7552:(t,e,i)=>{"use strict";i.d(e,{Providers:()=>n});var r=i(687),s=i(2136);function n({children:t}){return(0,r.jsx)(s.SessionProvider,{children:t})}},7629:(t,e,i)=>{Promise.resolve().then(i.bind(i,6544))},7785:(t,e,i)=>{Promise.resolve().then(i.t.bind(i,6444,23)),Promise.resolve().then(i.t.bind(i,6042,23)),Promise.resolve().then(i.t.bind(i,8170,23)),Promise.resolve().then(i.t.bind(i,9477,23)),Promise.resolve().then(i.t.bind(i,9345,23)),Promise.resolve().then(i.t.bind(i,2089,23)),Promise.resolve().then(i.t.bind(i,6577,23)),Promise.resolve().then(i.t.bind(i,1307,23))},7950:(t,e,i)=>{"use strict";i.r(e),i.d(e,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>h,routeModule:()=>c,tree:()=>u});var r=i(5239),s=i(8088),n=i(8170),o=i.n(n),a=i(893),l={};for(let t in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(l[t]=()=>a[t]);i.d(e,l);let u={children:["",{children:["demo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,3920)),"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\demo\\page.tsx"]}]},{metadata:{icon:[async t=>(await Promise.resolve().then(i.bind(i,440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,4431)),"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async t=>(await Promise.resolve().then(i.bind(i,440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,h=["C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\demo\\page.tsx"],d={require:i,loadChunk:()=>Promise.resolve()},c=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/demo/page",pathname:"/demo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},9121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:t=>{"use strict";t.exports=require("url")}};var e=require("../../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),r=e.X(0,[447,70,567],()=>i(7950));module.exports=r})();