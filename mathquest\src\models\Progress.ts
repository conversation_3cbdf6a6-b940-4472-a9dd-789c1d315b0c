import mongoose, { Document, Schema } from 'mongoose';

export interface IProgress extends Document {
  userId: mongoose.Types.ObjectId;
  grade: number;
  week: number;
  topicId: string;
  completed: boolean;
  score: number;
  timeSpent: number; // in minutes
  attempts: number;
  lastAttemptDate: Date;
  achievements: string[];
  createdAt: Date;
  updatedAt: Date;
}

const ProgressSchema: Schema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  grade: {
    type: Number,
    required: true,
    min: 1,
    max: 10
  },
  week: {
    type: Number,
    required: true,
    min: 1,
    max: 40
  },
  topicId: {
    type: String,
    required: true
  },
  completed: {
    type: Boolean,
    default: false
  },
  score: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  timeSpent: {
    type: Number,
    default: 0,
    min: 0
  },
  attempts: {
    type: Number,
    default: 0,
    min: 0
  },
  lastAttemptDate: {
    type: Date,
    default: Date.now
  },
  achievements: [{
    type: String
  }]
}, {
  timestamps: true
});

// Compound index for efficient queries
ProgressSchema.index({ userId: 1, grade: 1, week: 1 });
ProgressSchema.index({ userId: 1, completed: 1 });

export default mongoose.models.Progress || mongoose.model<IProgress>('Progress', ProgressSchema);
