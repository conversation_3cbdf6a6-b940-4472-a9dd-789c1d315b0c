// Comprehensive Mathematics Curriculum for Grades 1-10
// 40 weeks per academic year, structured week by week

export interface LearningOutcome {
  id: string;
  description: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface WeeklyTopic {
  week: number;
  title: string;
  description: string;
  topics: string[];
  learningOutcomes: LearningOutcome[];
  gameTypes: string[];
  estimatedHours: number;
}

export interface GradeCurriculum {
  grade: number;
  title: string;
  description: string;
  totalWeeks: number;
  weeks: WeeklyTopic[];
}

export const mathCurriculum: GradeCurriculum[] = [
  {
    grade: 1,
    title: "Grade 1 Mathematics - Foundation Building",
    description: "Introduction to numbers, basic counting, shapes, and simple addition/subtraction",
    totalWeeks: 40,
    weeks: [
      {
        week: 1,
        title: "Numbers 1-10",
        description: "Introduction to counting and recognizing numbers 1-10",
        topics: ["Counting objects", "Number recognition", "Number writing", "One-to-one correspondence"],
        learningOutcomes: [
          { id: "g1w1_1", description: "Count objects up to 10", difficulty: "easy" },
          { id: "g1w1_2", description: "Recognize and write numbers 1-10", difficulty: "easy" },
          { id: "g1w1_3", description: "Match numbers to quantities", difficulty: "easy" }
        ],
        gameTypes: ["counting-game", "number-matching", "drag-drop"],
        estimatedHours: 4
      },
      {
        week: 2,
        title: "Numbers 11-20",
        description: "Extending counting skills to numbers 11-20",
        topics: ["Counting 11-20", "Teen numbers", "Number patterns", "Before and after"],
        learningOutcomes: [
          { id: "g1w2_1", description: "Count objects up to 20", difficulty: "easy" },
          { id: "g1w2_2", description: "Identify numbers that come before and after", difficulty: "medium" },
          { id: "g1w2_3", description: "Recognize patterns in teen numbers", difficulty: "medium" }
        ],
        gameTypes: ["number-sequence", "pattern-recognition", "counting-game"],
        estimatedHours: 4
      },
      {
        week: 3,
        title: "Basic Shapes",
        description: "Introduction to 2D shapes and their properties",
        topics: ["Circle", "Square", "Triangle", "Rectangle", "Shape recognition"],
        learningOutcomes: [
          { id: "g1w3_1", description: "Identify basic 2D shapes", difficulty: "easy" },
          { id: "g1w3_2", description: "Describe shape properties", difficulty: "medium" },
          { id: "g1w3_3", description: "Find shapes in the environment", difficulty: "easy" }
        ],
        gameTypes: ["shape-matching", "shape-hunt", "drawing-game"],
        estimatedHours: 4
      },
      {
        week: 4,
        title: "Comparing Numbers",
        description: "Learning to compare quantities and numbers",
        topics: ["More than", "Less than", "Equal to", "Comparing groups", "Number comparison"],
        learningOutcomes: [
          { id: "g1w4_1", description: "Compare two groups of objects", difficulty: "easy" },
          { id: "g1w4_2", description: "Use terms more, less, equal", difficulty: "easy" },
          { id: "g1w4_3", description: "Compare numbers 1-20", difficulty: "medium" }
        ],
        gameTypes: ["comparison-game", "balance-scale", "sorting-game"],
        estimatedHours: 4
      },
      {
        week: 5,
        title: "Addition Introduction",
        description: "Basic addition concepts with objects and pictures",
        topics: ["Adding objects", "Addition stories", "Plus sign", "Sum", "Addition facts to 5"],
        learningOutcomes: [
          { id: "g1w5_1", description: "Add two groups of objects", difficulty: "easy" },
          { id: "g1w5_2", description: "Write simple addition sentences", difficulty: "medium" },
          { id: "g1w5_3", description: "Solve addition problems to 5", difficulty: "easy" }
        ],
        gameTypes: ["addition-blocks", "story-problems", "number-bonds"],
        estimatedHours: 5
      },
      {
        week: 6,
        title: "Subtraction Introduction",
        description: "Basic subtraction concepts with objects and pictures",
        topics: ["Taking away objects", "Subtraction stories", "Minus sign", "Difference", "Subtraction facts to 5"],
        learningOutcomes: [
          { id: "g1w6_1", description: "Subtract objects from a group", difficulty: "easy" },
          { id: "g1w6_2", description: "Write simple subtraction sentences", difficulty: "medium" },
          { id: "g1w6_3", description: "Solve subtraction problems to 5", difficulty: "easy" }
        ],
        gameTypes: ["subtraction-blocks", "story-problems", "number-bonds"],
        estimatedHours: 5
      },
      {
        week: 7,
        title: "Addition and Subtraction to 10",
        description: "Extending addition and subtraction skills to 10",
        topics: ["Addition facts to 10", "Subtraction facts to 10", "Fact families", "Missing numbers"],
        learningOutcomes: [
          { id: "g1w7_1", description: "Add and subtract within 10 fluently", difficulty: "medium" },
          { id: "g1w7_2", description: "Understand fact families", difficulty: "medium" },
          { id: "g1w7_3", description: "Find missing numbers in equations", difficulty: "hard" }
        ],
        gameTypes: ["fact-family-house", "missing-number", "speed-math"],
        estimatedHours: 6
      },
      {
        week: 8,
        title: "Measurement - Length",
        description: "Introduction to measuring length using non-standard units",
        topics: ["Comparing lengths", "Measuring with objects", "Longer/shorter", "Units of measurement"],
        learningOutcomes: [
          { id: "g1w8_1", description: "Compare lengths of objects", difficulty: "easy" },
          { id: "g1w8_2", description: "Measure using non-standard units", difficulty: "medium" },
          { id: "g1w8_3", description: "Order objects by length", difficulty: "medium" }
        ],
        gameTypes: ["measurement-game", "comparison-tool", "ordering-activity"],
        estimatedHours: 4
      }
    ]
  },
  {
    grade: 2,
    title: "Grade 2 Mathematics - Building Fluency",
    description: "Strengthening number sense, place value, and basic operations",
    totalWeeks: 40,
    weeks: [
      {
        week: 1,
        title: "Numbers to 100",
        description: "Understanding place value and counting to 100",
        topics: ["Counting to 100", "Place value", "Tens and ones", "Number patterns"],
        learningOutcomes: [
          { id: "g2w1_1", description: "Count by 1s, 2s, 5s, and 10s to 100", difficulty: "easy" },
          { id: "g2w1_2", description: "Understand place value in 2-digit numbers", difficulty: "medium" },
          { id: "g2w1_3", description: "Identify patterns in number sequences", difficulty: "medium" }
        ],
        gameTypes: ["place-value-blocks", "number-patterns", "counting-by"],
        estimatedHours: 5
      },
      {
        week: 2,
        title: "Addition with Regrouping",
        description: "Two-digit addition with and without regrouping",
        topics: ["Adding 2-digit numbers", "Regrouping", "Mental math strategies", "Estimation"],
        learningOutcomes: [
          { id: "g2w2_1", description: "Add 2-digit numbers without regrouping", difficulty: "medium" },
          { id: "g2w2_2", description: "Add 2-digit numbers with regrouping", difficulty: "hard" },
          { id: "g2w2_3", description: "Estimate sums", difficulty: "medium" }
        ],
        gameTypes: ["base-ten-blocks", "mental-math", "estimation-game"],
        estimatedHours: 6
      }
    ]
  }
];

// This is a sample structure - in production, this would include all 40 weeks for each grade
export const getGradeCurriculum = (grade: number): GradeCurriculum | undefined => {
  return mathCurriculum.find(curriculum => curriculum.grade === grade);
};

export const getWeeklyTopic = (grade: number, week: number): WeeklyTopic | undefined => {
  const gradeCurriculum = getGradeCurriculum(grade);
  return gradeCurriculum?.weeks.find(weeklyTopic => weeklyTopic.week === week);
};
