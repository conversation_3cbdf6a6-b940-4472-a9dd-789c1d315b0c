(()=>{var e={};e.id=436,e.ids=[436],e.modules={365:(e,t,i)=>{Promise.resolve().then(i.bind(i,3920))},440:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>r});var s=i(1658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},2318:(e,t,i)=>{Promise.resolve().then(i.bind(i,7552))},2582:(e,t,i)=>{Promise.resolve().then(i.bind(i,3622))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3622:(e,t,i)=>{"use strict";i.d(t,{Providers:()=>s});let s=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\components\\Providers.tsx","Providers")},3710:(e,t,i)=>{"use strict";let s;i.r(t),i.d(t,{default:()=>a3});var r,n,a=i(687),o=i(3210);function l(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function d(e){let t=[{},{}];return e?.values.forEach((e,i)=>{t[0][i]=e.get(),t[1][i]=e.getVelocity()}),t}function u(e,t,i,s){if("function"==typeof t){let[r,n]=d(s);t=t(void 0!==i?i:e.custom,r,n)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[r,n]=d(s);t=t(void 0!==i?i:e.custom,r,n)}return t}function h(e,t,i){let s=e.getProps();return u(s,t,void 0!==i?i:s.custom,e)}function c(e,t){return e?.[t]??e?.default??e}let m=e=>e,p={},f=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],g={value:null,addProjectionMetrics:null};function y(e,t){let i=!1,s=!0,r={delta:0,timestamp:0,isProcessing:!1},n=()=>i=!0,a=f.reduce((e,i)=>(e[i]=function(e,t){let i=new Set,s=new Set,r=!1,n=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function d(t){a.has(t)&&(u.schedule(t),e()),l++,t(o)}let u={schedule:(e,t=!1,n=!1)=>{let o=n&&r?i:s;return t&&a.add(e),o.has(e)||o.add(e),e},cancel:e=>{s.delete(e),a.delete(e)},process:e=>{if(o=e,r){n=!0;return}r=!0,[i,s]=[s,i],i.forEach(d),t&&g.value&&g.value.frameloop[t].push(l),l=0,i.clear(),r=!1,n&&(n=!1,u.process(e))}};return u}(n,t?i:void 0),e),{}),{setup:o,read:l,resolveKeyframes:d,preUpdate:u,update:h,preRender:c,render:m,postRender:y}=a,x=()=>{let n=p.useManualTiming?r.timestamp:performance.now();i=!1,p.useManualTiming||(r.delta=s?1e3/60:Math.max(Math.min(n-r.timestamp,40),1)),r.timestamp=n,r.isProcessing=!0,o.process(r),l.process(r),d.process(r),u.process(r),h.process(r),c.process(r),m.process(r),y.process(r),r.isProcessing=!1,i&&t&&(s=!1,e(x))},v=()=>{i=!0,s=!0,r.isProcessing||e(x)};return{schedule:f.reduce((e,t)=>{let s=a[t];return e[t]=(e,t=!1,r=!1)=>(i||v(),s.schedule(e,t,r)),e},{}),cancel:e=>{for(let t=0;t<f.length;t++)a[f[t]].cancel(e)},state:r,steps:a}}let{schedule:x,cancel:v,state:b,steps:w}=y("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:m,!0),j=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],k=new Set(j),P=new Set(["width","height","top","left","right","bottom",...j]);function T(e,t){-1===e.indexOf(t)&&e.push(t)}function S(e,t){let i=e.indexOf(t);i>-1&&e.splice(i,1)}class M{constructor(){this.subscriptions=[]}add(e){return T(this.subscriptions,e),()=>S(this.subscriptions,e)}notify(e,t,i){let s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](e,t,i);else for(let r=0;r<s;r++){let s=this.subscriptions[r];s&&s(e,t,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function N(){s=void 0}let A={now:()=>(void 0===s&&A.set(b.isProcessing||p.useManualTiming?b.timestamp:performance.now()),s),set:e=>{s=e,queueMicrotask(N)}},C=e=>!isNaN(parseFloat(e)),E={current:void 0};class V{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let i=A.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=A.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=C(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new M);let i=this.events[e].add(t);return"change"===e?()=>{i(),x.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,i){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-i}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return E.current&&E.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=A.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function D(e,t){return new V(e,t)}let R=e=>Array.isArray(e),L=e=>!!(e&&e.getVelocity);function F(e,t){let i=e.getValue("willChange");if(L(i)&&i.add)return i.add(t);if(!i&&p.WillChange){let i=new p.WillChange("auto");e.addValue("willChange",i),i.add(t)}}let B=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),z="data-"+B("framerAppearId"),O=(e,t)=>i=>t(e(i)),I=(...e)=>e.reduce(O),$=(e,t,i)=>i>t?t:i<e?e:i,U=e=>1e3*e,W=e=>e/1e3,q={layout:0,mainThread:0,waapi:0},H=()=>{},G=()=>{},Y=e=>t=>"string"==typeof t&&t.startsWith(e),X=Y("--"),_=Y("var(--"),K=e=>!!_(e)&&Z.test(e.split("/*")[0].trim()),Z=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Q={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},J={...Q,transform:e=>$(0,1,e)},ee={...Q,default:1},et=e=>Math.round(1e5*e)/1e5,ei=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,es=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,er=(e,t)=>i=>!!("string"==typeof i&&es.test(i)&&i.startsWith(e)||t&&null!=i&&Object.prototype.hasOwnProperty.call(i,t)),en=(e,t,i)=>s=>{if("string"!=typeof s)return s;let[r,n,a,o]=s.match(ei);return{[e]:parseFloat(r),[t]:parseFloat(n),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},ea=e=>$(0,255,e),eo={...Q,transform:e=>Math.round(ea(e))},el={test:er("rgb","red"),parse:en("red","green","blue"),transform:({red:e,green:t,blue:i,alpha:s=1})=>"rgba("+eo.transform(e)+", "+eo.transform(t)+", "+eo.transform(i)+", "+et(J.transform(s))+")"},ed={test:er("#"),parse:function(e){let t="",i="",s="",r="";return e.length>5?(t=e.substring(1,3),i=e.substring(3,5),s=e.substring(5,7),r=e.substring(7,9)):(t=e.substring(1,2),i=e.substring(2,3),s=e.substring(3,4),r=e.substring(4,5),t+=t,i+=i,s+=s,r+=r),{red:parseInt(t,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:r?parseInt(r,16)/255:1}},transform:el.transform},eu=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),eh=eu("deg"),ec=eu("%"),em=eu("px"),ep=eu("vh"),ef=eu("vw"),eg={...ec,parse:e=>ec.parse(e)/100,transform:e=>ec.transform(100*e)},ey={test:er("hsl","hue"),parse:en("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:i,alpha:s=1})=>"hsla("+Math.round(e)+", "+ec.transform(et(t))+", "+ec.transform(et(i))+", "+et(J.transform(s))+")"},ex={test:e=>el.test(e)||ed.test(e)||ey.test(e),parse:e=>el.test(e)?el.parse(e):ey.test(e)?ey.parse(e):ed.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?el.transform(e):ey.transform(e)},ev=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eb="number",ew="color",ej=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ek(e){let t=e.toString(),i=[],s={color:[],number:[],var:[]},r=[],n=0,a=t.replace(ej,e=>(ex.test(e)?(s.color.push(n),r.push(ew),i.push(ex.parse(e))):e.startsWith("var(")?(s.var.push(n),r.push("var"),i.push(e)):(s.number.push(n),r.push(eb),i.push(parseFloat(e))),++n,"${}")).split("${}");return{values:i,split:a,indexes:s,types:r}}function eP(e){return ek(e).values}function eT(e){let{split:t,types:i}=ek(e),s=t.length;return e=>{let r="";for(let n=0;n<s;n++)if(r+=t[n],void 0!==e[n]){let t=i[n];t===eb?r+=et(e[n]):t===ew?r+=ex.transform(e[n]):r+=e[n]}return r}}let eS=e=>"number"==typeof e?0:e,eM={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(ei)?.length||0)+(e.match(ev)?.length||0)>0},parse:eP,createTransformer:eT,getAnimatableNone:function(e){let t=eP(e);return eT(e)(t.map(eS))}};function eN(e,t,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?e+(t-e)*6*i:i<.5?t:i<2/3?e+(t-e)*(2/3-i)*6:e}function eA(e,t){return i=>i>0?t:e}let eC=(e,t,i)=>e+(t-e)*i,eE=(e,t,i)=>{let s=e*e,r=i*(t*t-s)+s;return r<0?0:Math.sqrt(r)},eV=[ed,el,ey],eD=e=>eV.find(t=>t.test(e));function eR(e){let t=eD(e);if(H(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let i=t.parse(e);return t===ey&&(i=function({hue:e,saturation:t,lightness:i,alpha:s}){e/=360,i/=100;let r=0,n=0,a=0;if(t/=100){let s=i<.5?i*(1+t):i+t-i*t,o=2*i-s;r=eN(o,s,e+1/3),n=eN(o,s,e),a=eN(o,s,e-1/3)}else r=n=a=i;return{red:Math.round(255*r),green:Math.round(255*n),blue:Math.round(255*a),alpha:s}}(i)),i}let eL=(e,t)=>{let i=eR(e),s=eR(t);if(!i||!s)return eA(e,t);let r={...i};return e=>(r.red=eE(i.red,s.red,e),r.green=eE(i.green,s.green,e),r.blue=eE(i.blue,s.blue,e),r.alpha=eC(i.alpha,s.alpha,e),el.transform(r))},eF=new Set(["none","hidden"]);function eB(e,t){return i=>eC(e,t,i)}function ez(e){return"number"==typeof e?eB:"string"==typeof e?K(e)?eA:ex.test(e)?eL:e$:Array.isArray(e)?eO:"object"==typeof e?ex.test(e)?eL:eI:eA}function eO(e,t){let i=[...e],s=i.length,r=e.map((e,i)=>ez(e)(e,t[i]));return e=>{for(let t=0;t<s;t++)i[t]=r[t](e);return i}}function eI(e,t){let i={...e,...t},s={};for(let r in i)void 0!==e[r]&&void 0!==t[r]&&(s[r]=ez(e[r])(e[r],t[r]));return e=>{for(let t in s)i[t]=s[t](e);return i}}let e$=(e,t)=>{let i=eM.createTransformer(t),s=ek(e),r=ek(t);return s.indexes.var.length===r.indexes.var.length&&s.indexes.color.length===r.indexes.color.length&&s.indexes.number.length>=r.indexes.number.length?eF.has(e)&&!r.values.length||eF.has(t)&&!s.values.length?function(e,t){return eF.has(e)?i=>i<=0?e:t:i=>i>=1?t:e}(e,t):I(eO(function(e,t){let i=[],s={color:0,var:0,number:0};for(let r=0;r<t.values.length;r++){let n=t.types[r],a=e.indexes[n][s[n]],o=e.values[a]??0;i[r]=o,s[n]++}return i}(s,r),r.values),i):(H(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eA(e,t))};function eU(e,t,i){return"number"==typeof e&&"number"==typeof t&&"number"==typeof i?eC(e,t,i):ez(e)(e,t)}let eW=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>x.update(t,e),stop:()=>v(t),now:()=>b.isProcessing?b.timestamp:A.now()}},eq=(e,t,i=10)=>{let s="",r=Math.max(Math.round(t/i),2);for(let t=0;t<r;t++)s+=e(t/(r-1))+", ";return`linear(${s.substring(0,s.length-2)})`};function eH(e){let t=0,i=e.next(t);for(;!i.done&&t<2e4;)t+=50,i=e.next(t);return t>=2e4?1/0:t}function eG(e,t,i){var s,r;let n=Math.max(t-5,0);return s=i-e(n),(r=t-n)?1e3/r*s:0}let eY={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eX(e,t){return e*Math.sqrt(1-t*t)}let e_=["duration","bounce"],eK=["stiffness","damping","mass"];function eZ(e,t){return t.some(t=>void 0!==e[t])}function eQ(e=eY.visualDuration,t=eY.bounce){let i,s="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:r,restDelta:n}=s,a=s.keyframes[0],o=s.keyframes[s.keyframes.length-1],l={done:!1,value:a},{stiffness:d,damping:u,mass:h,duration:c,velocity:m,isResolvedFromDuration:p}=function(e){let t={velocity:eY.velocity,stiffness:eY.stiffness,damping:eY.damping,mass:eY.mass,isResolvedFromDuration:!1,...e};if(!eZ(e,eK)&&eZ(e,e_))if(e.visualDuration){let i=2*Math.PI/(1.2*e.visualDuration),s=i*i,r=2*$(.05,1,1-(e.bounce||0))*Math.sqrt(s);t={...t,mass:eY.mass,stiffness:s,damping:r}}else{let i=function({duration:e=eY.duration,bounce:t=eY.bounce,velocity:i=eY.velocity,mass:s=eY.mass}){let r,n;H(e<=U(eY.maxDuration),"Spring duration must be 10 seconds or less");let a=1-t;a=$(eY.minDamping,eY.maxDamping,a),e=$(eY.minDuration,eY.maxDuration,W(e)),a<1?(r=t=>{let s=t*a,r=s*e;return .001-(s-i)/eX(t,a)*Math.exp(-r)},n=t=>{let s=t*a*e,n=Math.pow(a,2)*Math.pow(t,2)*e,o=Math.exp(-s),l=eX(Math.pow(t,2),a);return(s*i+i-n)*o*(-r(t)+.001>0?-1:1)/l}):(r=t=>-.001+Math.exp(-t*e)*((t-i)*e+1),n=t=>e*e*(i-t)*Math.exp(-t*e));let o=function(e,t,i){let s=i;for(let i=1;i<12;i++)s-=e(s)/t(s);return s}(r,n,5/e);if(e=U(e),isNaN(o))return{stiffness:eY.stiffness,damping:eY.damping,duration:e};{let t=Math.pow(o,2)*s;return{stiffness:t,damping:2*a*Math.sqrt(s*t),duration:e}}}(e);(t={...t,...i,mass:eY.mass}).isResolvedFromDuration=!0}return t}({...s,velocity:-W(s.velocity||0)}),f=m||0,g=u/(2*Math.sqrt(d*h)),y=o-a,x=W(Math.sqrt(d/h)),v=5>Math.abs(y);if(r||(r=v?eY.restSpeed.granular:eY.restSpeed.default),n||(n=v?eY.restDelta.granular:eY.restDelta.default),g<1){let e=eX(x,g);i=t=>o-Math.exp(-g*x*t)*((f+g*x*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===g)i=e=>o-Math.exp(-x*e)*(y+(f+x*y)*e);else{let e=x*Math.sqrt(g*g-1);i=t=>{let i=Math.exp(-g*x*t),s=Math.min(e*t,300);return o-i*((f+g*x*y)*Math.sinh(s)+e*y*Math.cosh(s))/e}}let b={calculatedDuration:p&&c||null,next:e=>{let t=i(e);if(p)l.done=e>=c;else{let s=0===e?f:0;g<1&&(s=0===e?U(f):eG(i,e,t));let a=Math.abs(o-t)<=n;l.done=Math.abs(s)<=r&&a}return l.value=l.done?o:t,l},toString:()=>{let e=Math.min(eH(b),2e4),t=eq(t=>b.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return b}function eJ({keyframes:e,velocity:t=0,power:i=.8,timeConstant:s=325,bounceDamping:r=10,bounceStiffness:n=500,modifyTarget:a,min:o,max:l,restDelta:d=.5,restSpeed:u}){let h,c,m=e[0],p={done:!1,value:m},f=e=>void 0!==o&&e<o||void 0!==l&&e>l,g=e=>void 0===o?l:void 0===l||Math.abs(o-e)<Math.abs(l-e)?o:l,y=i*t,x=m+y,v=void 0===a?x:a(x);v!==x&&(y=v-m);let b=e=>-y*Math.exp(-e/s),w=e=>v+b(e),j=e=>{let t=b(e),i=w(e);p.done=Math.abs(t)<=d,p.value=p.done?v:i},k=e=>{f(p.value)&&(h=e,c=eQ({keyframes:[p.value,g(p.value)],velocity:eG(w,e,p.value),damping:r,stiffness:n,restDelta:d,restSpeed:u}))};return k(0),{calculatedDuration:null,next:e=>{let t=!1;return(c||void 0!==h||(t=!0,j(e),k(e)),void 0!==h&&e>=h)?c.next(e-h):(t||j(e),p)}}}eQ.applyToOptions=e=>{let t=function(e,t=100,i){let s=i({...e,keyframes:[0,t]}),r=Math.min(eH(s),2e4);return{type:"keyframes",ease:e=>s.next(r*e).value/t,duration:W(r)}}(e,100,eQ);return e.ease=t.ease,e.duration=U(t.duration),e.type="keyframes",e};let e0=(e,t,i)=>(((1-3*i+3*t)*e+(3*i-6*t))*e+3*t)*e;function e1(e,t,i,s){if(e===t&&i===s)return m;let r=t=>(function(e,t,i,s,r){let n,a,o=0;do(n=e0(a=t+(i-t)/2,s,r)-e)>0?i=a:t=a;while(Math.abs(n)>1e-7&&++o<12);return a})(t,0,1,e,i);return e=>0===e||1===e?e:e0(r(e),t,s)}let e2=e1(.42,0,1,1),e5=e1(0,0,.58,1),e6=e1(.42,0,.58,1),e3=e=>Array.isArray(e)&&"number"!=typeof e[0],e4=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e8=e=>t=>1-e(1-t),e9=e1(.33,1.53,.69,.99),e7=e8(e9),te=e4(e7),tt=e=>(e*=2)<1?.5*e7(e):.5*(2-Math.pow(2,-10*(e-1))),ti=e=>1-Math.sin(Math.acos(e)),ts=e8(ti),tr=e4(ti),tn=e=>Array.isArray(e)&&"number"==typeof e[0],ta={linear:m,easeIn:e2,easeInOut:e6,easeOut:e5,circIn:ti,circInOut:tr,circOut:ts,backIn:e7,backInOut:te,backOut:e9,anticipate:tt},to=e=>"string"==typeof e,tl=e=>{if(tn(e)){G(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,i,s,r]=e;return e1(t,i,s,r)}return to(e)?(G(void 0!==ta[e],`Invalid easing type '${e}'`),ta[e]):e},td=(e,t,i)=>{let s=t-e;return 0===s?1:(i-e)/s};function tu({duration:e=300,keyframes:t,times:i,ease:s="easeInOut"}){var r;let n=e3(s)?s.map(tl):tl(s),a={done:!1,value:t[0]},o=function(e,t,{clamp:i=!0,ease:s,mixer:r}={}){let n=e.length;if(G(n===t.length,"Both input and output ranges must be the same length"),1===n)return()=>t[0];if(2===n&&t[0]===t[1])return()=>t[1];let a=e[0]===e[1];e[0]>e[n-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,i){let s=[],r=i||p.mix||eU,n=e.length-1;for(let i=0;i<n;i++){let n=r(e[i],e[i+1]);t&&(n=I(Array.isArray(t)?t[i]||m:t,n)),s.push(n)}return s}(t,s,r),l=o.length,d=i=>{if(a&&i<e[0])return t[0];let s=0;if(l>1)for(;s<e.length-2&&!(i<e[s+1]);s++);let r=td(e[s],e[s+1],i);return o[s](r)};return i?t=>d($(e[0],e[n-1],t)):d}((r=i&&i.length===t.length?i:function(e){let t=[0];return!function(e,t){let i=e[e.length-1];for(let s=1;s<=t;s++){let r=td(0,t,s);e.push(eC(i,1,r))}}(t,e.length-1),t}(t),r.map(t=>t*e)),t,{ease:Array.isArray(n)?n:t.map(()=>n||e6).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(a.value=o(t),a.done=t>=e,a)}}let th=e=>null!==e;function tc(e,{repeat:t,repeatType:i="loop"},s,r=1){let n=e.filter(th),a=r<0||t&&"loop"!==i&&t%2==1?0:n.length-1;return a&&void 0!==s?s:n[a]}let tm={decay:eJ,inertia:eJ,tween:tu,keyframes:tu,spring:eQ};function tp(e){"string"==typeof e.type&&(e.type=tm[e.type])}class tf{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let tg=e=>e/100;class ty extends tf{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==A.now()&&this.tick(A.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},q.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;tp(e);let{type:t=tu,repeat:i=0,repeatDelay:s=0,repeatType:r,velocity:n=0}=e,{keyframes:a}=e,o=t||tu;o!==tu&&"number"!=typeof a[0]&&(this.mixKeyframes=I(tg,eU(a[0],a[1])),a=[0,100]);let l=o({...e,keyframes:a});"mirror"===r&&(this.mirroredGenerator=o({...e,keyframes:[...a].reverse(),velocity:-n})),null===l.calculatedDuration&&(l.calculatedDuration=eH(l));let{calculatedDuration:d}=l;this.calculatedDuration=d,this.resolvedDuration=d+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:i,totalDuration:s,mixKeyframes:r,mirroredGenerator:n,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:d,repeat:u,repeatType:h,repeatDelay:c,type:m,onUpdate:p,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-s/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>s;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let x=this.currentTime,v=i;if(u){let e=Math.min(this.currentTime,s)/a,t=Math.floor(e),i=e%1;!i&&e>=1&&(i=1),1===i&&t--,(t=Math.min(t,u+1))%2&&("reverse"===h?(i=1-i,c&&(i-=c/a)):"mirror"===h&&(v=n)),x=$(0,1,i)*a}let b=y?{done:!1,value:d[0]}:v.next(x);r&&(b.value=r(b.value));let{done:w}=b;y||null===o||(w=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);let j=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return j&&m!==eJ&&(b.value=tc(d,this.options,f,this.speed)),p&&p(b.value),j&&this.finish(),b}then(e,t){return this.finished.then(e,t)}get duration(){return W(this.calculatedDuration)}get time(){return W(this.currentTime)}set time(e){e=U(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(A.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=W(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=eW,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=t??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(A.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,q.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tx=e=>180*e/Math.PI,tv=e=>tw(tx(Math.atan2(e[1],e[0]))),tb={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tv,rotateZ:tv,skewX:e=>tx(Math.atan(e[1])),skewY:e=>tx(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},tw=e=>((e%=360)<0&&(e+=360),e),tj=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tk=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tP={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tj,scaleY:tk,scale:e=>(tj(e)+tk(e))/2,rotateX:e=>tw(tx(Math.atan2(e[6],e[5]))),rotateY:e=>tw(tx(Math.atan2(-e[2],e[0]))),rotateZ:tv,rotate:tv,skewX:e=>tx(Math.atan(e[4])),skewY:e=>tx(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tT(e){return+!!e.includes("scale")}function tS(e,t){let i,s;if(!e||"none"===e)return tT(t);let r=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=tP,s=r;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=tb,s=t}if(!s)return tT(t);let n=i[t],a=s[1].split(",").map(tN);return"function"==typeof n?n(a):a[n]}let tM=(e,t)=>{let{transform:i="none"}=getComputedStyle(e);return tS(i,t)};function tN(e){return parseFloat(e.trim())}let tA=e=>e===Q||e===em,tC=new Set(["x","y","z"]),tE=j.filter(e=>!tC.has(e)),tV={width:({x:e},{paddingLeft:t="0",paddingRight:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),height:({y:e},{paddingTop:t="0",paddingBottom:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tS(t,"x"),y:(e,{transform:t})=>tS(t,"y")};tV.translateX=tV.x,tV.translateY=tV.y;let tD=new Set,tR=!1,tL=!1,tF=!1;function tB(){if(tL){let e=Array.from(tD).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),i=new Map;t.forEach(e=>{let t=function(e){let t=[];return tE.forEach(i=>{let s=e.getValue(i);void 0!==s&&(t.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),t}(e);t.length&&(i.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=i.get(e);t&&t.forEach(([t,i])=>{e.getValue(t)?.set(i)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tL=!1,tR=!1,tD.forEach(e=>e.complete(tF)),tD.clear()}function tz(){tD.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tL=!0)})}class tO{constructor(e,t,i,s,r,n=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=i,this.motionValue=s,this.element=r,this.isAsync=n}scheduleResolve(){this.state="scheduled",this.isAsync?(tD.add(this),tR||(tR=!0,x.read(tz),x.resolveKeyframes(tB))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:i,motionValue:s}=this;if(null===e[0]){let r=s?.get(),n=e[e.length-1];if(void 0!==r)e[0]=r;else if(i&&t){let s=i.readValue(t,n);null!=s&&(e[0]=s)}void 0===e[0]&&(e[0]=n),s&&void 0===r&&s.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tD.delete(this)}cancel(){"scheduled"===this.state&&(tD.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tI=e=>e.startsWith("--");function t$(e){let t;return()=>(void 0===t&&(t=e()),t)}let tU=t$(()=>void 0!==window.ScrollTimeline),tW={},tq=function(e,t){let i=t$(e);return()=>tW[t]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tH=([e,t,i,s])=>`cubic-bezier(${e}, ${t}, ${i}, ${s})`,tG={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tH([0,.65,.55,1]),circOut:tH([.55,0,1,.45]),backIn:tH([.31,.01,.66,-.59]),backOut:tH([.33,1.53,.69,.99])};function tY(e){return"function"==typeof e&&"applyToOptions"in e}class tX extends tf{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:i,keyframes:s,pseudoElement:r,allowFlatten:n=!1,finalKeyframe:a,onComplete:o}=e;this.isPseudoElement=!!r,this.allowFlatten=n,this.options=e,G("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return tY(e)&&tq()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,i,{delay:s=0,duration:r=300,repeat:n=0,repeatType:a="loop",ease:o="easeOut",times:l}={},d){let u={[t]:i};l&&(u.offset=l);let h=function e(t,i){if(t)return"function"==typeof t?tq()?eq(t,i):"ease-out":tn(t)?tH(t):Array.isArray(t)?t.map(t=>e(t,i)||tG.easeOut):tG[t]}(o,r);Array.isArray(h)&&(u.easing=h),g.value&&q.waapi++;let c={delay:s,duration:r,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:n+1,direction:"reverse"===a?"alternate":"normal"};d&&(c.pseudoElement=d);let m=e.animate(u,c);return g.value&&m.finished.finally(()=>{q.waapi--}),m}(t,i,s,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let e=tc(s,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,i){tI(t)?e.style.setProperty(t,i):e.style[t]=i}(t,i,e),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return W(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return W(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=U(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&tU())?(this.animation.timeline=e,m):t(this)}}let t_={anticipate:tt,backInOut:te,circInOut:tr};class tK extends tX{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in t_&&(e.ease=t_[e.ease])}(e),tp(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:i,onComplete:s,element:r,...n}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let a=new ty({...n,autoplay:!1}),o=U(this.finishedTime??this.time);t.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}let tZ=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eM.test(e)||"0"===e)&&!e.startsWith("url("));function tQ(e){return"object"==typeof e&&null!==e}function tJ(e){return tQ(e)&&"offsetHeight"in e}let t0=new Set(["opacity","clipPath","filter","transform"]),t1=t$(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class t2 extends tf{constructor({autoplay:e=!0,delay:t=0,type:i="keyframes",repeat:s=0,repeatDelay:r=0,repeatType:n="loop",keyframes:a,name:o,motionValue:l,element:d,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=A.now();let h={autoplay:e,delay:t,type:i,repeat:s,repeatDelay:r,repeatType:n,name:o,motionValue:l,element:d,...u},c=d?.KeyframeResolver||tO;this.keyframeResolver=new c(a,(e,t,i)=>this.onKeyframesResolved(e,t,h,!i),o,l,d),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,i,s){this.keyframeResolver=void 0;let{name:r,type:n,velocity:a,delay:o,isHandoff:l,onUpdate:d}=i;this.resolvedAt=A.now(),!function(e,t,i,s){let r=e[0];if(null===r)return!1;if("display"===t||"visibility"===t)return!0;let n=e[e.length-1],a=tZ(r,t),o=tZ(n,t);return H(a===o,`You are trying to animate ${t} from "${r}" to "${n}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${n} via the \`style\` property.`),!!a&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let i=0;i<e.length;i++)if(e[i]!==t)return!0}(e)||("spring"===i||tY(i))&&s)}(e,r,n,a)&&((p.instantAnimations||!o)&&d?.(tc(e,i,t)),e[0]=e[e.length-1],i.duration=0,i.repeat=0);let u={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...i,keyframes:e},h=!l&&function(e){let{motionValue:t,name:i,repeatDelay:s,repeatType:r,damping:n,type:a}=e;if(!tJ(t?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=t.owner.getProps();return t1()&&i&&t0.has(i)&&("transform"!==i||!l)&&!o&&!s&&"mirror"!==r&&0!==n&&"inertia"!==a}(u)?new tK({...u,element:u.motionValue.owner.current}):new ty(u);h.finished.then(()=>this.notifyFinished()).catch(m),this.pendingTimeline&&(this.stopTimeline=h.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=h}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tF=!0,tz(),tB(),tF=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let t5=e=>null!==e,t6={type:"spring",stiffness:500,damping:25,restSpeed:10},t3=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),t4={type:"keyframes",duration:.8},t8={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},t9=(e,{keyframes:t})=>t.length>2?t4:k.has(e)?e.startsWith("scale")?t3(t[1]):t6:t8,t7=(e,t,i,s={},r,n)=>a=>{let o=c(s,e)||{},l=o.delay||s.delay||0,{elapsed:d=0}=s;d-=U(l);let u={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:t.getVelocity(),...o,delay:-d,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:e,motionValue:t,element:n?void 0:r};!function({when:e,delay:t,delayChildren:i,staggerChildren:s,staggerDirection:r,repeat:n,repeatType:a,repeatDelay:o,from:l,elapsed:d,...u}){return!!Object.keys(u).length}(o)&&Object.assign(u,t9(e,u)),u.duration&&(u.duration=U(u.duration)),u.repeatDelay&&(u.repeatDelay=U(u.repeatDelay)),void 0!==u.from&&(u.keyframes[0]=u.from);let h=!1;if(!1!==u.type&&(0!==u.duration||u.repeatDelay)||(u.duration=0,0===u.delay&&(h=!0)),(p.instantAnimations||p.skipAnimations)&&(h=!0,u.duration=0,u.delay=0),u.allowFlatten=!o.type&&!o.ease,h&&!n&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:i="loop"},s){let r=e.filter(t5),n=t&&"loop"!==i&&t%2==1?0:r.length-1;return r[n]}(u.keyframes,o);if(void 0!==e)return void x.update(()=>{u.onUpdate(e),u.onComplete()})}return o.isSync?new ty(u):new t2(u)};function ie(e,t,{delay:i=0,transitionOverride:s,type:r}={}){let{transition:n=e.getDefaultTransition(),transitionEnd:a,...o}=t;s&&(n=s);let l=[],d=r&&e.animationState&&e.animationState.getState()[r];for(let t in o){let s=e.getValue(t,e.latestValues[t]??null),r=o[t];if(void 0===r||d&&function({protectedKeys:e,needsAnimating:t},i){let s=e.hasOwnProperty(i)&&!0!==t[i];return t[i]=!1,s}(d,t))continue;let a={delay:i,...c(n||{},t)},u=s.get();if(void 0!==u&&!s.isAnimating&&!Array.isArray(r)&&r===u&&!a.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let i=e.props[z];if(i){let e=window.MotionHandoffAnimation(i,t,x);null!==e&&(a.startTime=e,h=!0)}}F(e,t),s.start(t7(t,s,r,e.shouldReduceMotion&&P.has(t)?{type:!1}:a,e,h));let m=s.animation;m&&l.push(m)}return a&&Promise.all(l).then(()=>{x.update(()=>{a&&function(e,t){let{transitionEnd:i={},transition:s={},...r}=h(e,t)||{};for(let t in r={...r,...i}){var n;let i=R(n=r[t])?n[n.length-1]||0:n;e.hasValue(t)?e.getValue(t).set(i):e.addValue(t,D(i))}}(e,a)})}),l}function it(e,t,i={}){let s=h(e,t,"exit"===i.type?e.presenceContext?.custom:void 0),{transition:r=e.getDefaultTransition()||{}}=s||{};i.transitionOverride&&(r=i.transitionOverride);let n=s?()=>Promise.all(ie(e,s,i)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(s=0)=>{let{delayChildren:n=0,staggerChildren:a,staggerDirection:o}=r;return function(e,t,i=0,s=0,r=1,n){let a=[],o=(e.variantChildren.size-1)*s,l=1===r?(e=0)=>e*s:(e=0)=>o-e*s;return Array.from(e.variantChildren).sort(ii).forEach((e,s)=>{e.notify("AnimationStart",t),a.push(it(e,t,{...n,delay:i+l(s)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,n+s,a,o,i)}:()=>Promise.resolve(),{when:o}=r;if(!o)return Promise.all([n(),a(i.delay)]);{let[e,t]="beforeChildren"===o?[n,a]:[a,n];return e().then(()=>t())}}function ii(e,t){return e.sortNodePosition(t)}function is(e,t){if(!Array.isArray(t))return!1;let i=t.length;if(i!==e.length)return!1;for(let s=0;s<i;s++)if(t[s]!==e[s])return!1;return!0}function ir(e){return"string"==typeof e||Array.isArray(e)}let ia=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],io=["initial",...ia],il=io.length,id=[...ia].reverse(),iu=ia.length;function ih(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ic(){return{animate:ih(!0),whileInView:ih(),whileHover:ih(),whileTap:ih(),whileDrag:ih(),whileFocus:ih(),exit:ih()}}class im{constructor(e){this.isMounted=!1,this.node=e}update(){}}class ip extends im{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:i})=>(function(e,t,i={}){let s;if(e.notify("AnimationStart",t),Array.isArray(t))s=Promise.all(t.map(t=>it(e,t,i)));else if("string"==typeof t)s=it(e,t,i);else{let r="function"==typeof t?h(e,t,i.custom):t;s=Promise.all(ie(e,r,i))}return s.then(()=>{e.notify("AnimationComplete",t)})})(e,t,i))),i=ic(),s=!0,r=t=>(i,s)=>{let r=h(e,s,"exit"===t?e.presenceContext?.custom:void 0);if(r){let{transition:e,transitionEnd:t,...s}=r;i={...i,...s,...t}}return i};function n(n){let{props:a}=e,o=function e(t){if(!t)return;if(!t.isControllingVariants){let i=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(i.initial=t.props.initial),i}let i={};for(let e=0;e<il;e++){let s=io[e],r=t.props[s];(ir(r)||!1===r)&&(i[s]=r)}return i}(e.parent)||{},d=[],u=new Set,c={},m=1/0;for(let t=0;t<iu;t++){var p,f;let h=id[t],g=i[h],y=void 0!==a[h]?a[h]:o[h],x=ir(y),v=h===n?g.isActive:null;!1===v&&(m=t);let b=y===o[h]&&y!==a[h]&&x;if(b&&s&&e.manuallyAnimateOnMount&&(b=!1),g.protectedKeys={...c},!g.isActive&&null===v||!y&&!g.prevProp||l(y)||"boolean"==typeof y)continue;let w=(p=g.prevProp,"string"==typeof(f=y)?f!==p:!!Array.isArray(f)&&!is(f,p)),j=w||h===n&&g.isActive&&!b&&x||t>m&&x,k=!1,P=Array.isArray(y)?y:[y],T=P.reduce(r(h),{});!1===v&&(T={});let{prevResolvedValues:S={}}=g,M={...S,...T},N=t=>{j=!0,u.has(t)&&(k=!0,u.delete(t)),g.needsAnimating[t]=!0;let i=e.getValue(t);i&&(i.liveStyle=!1)};for(let e in M){let t=T[e],i=S[e];if(c.hasOwnProperty(e))continue;let s=!1;(R(t)&&R(i)?is(t,i):t===i)?void 0!==t&&u.has(e)?N(e):g.protectedKeys[e]=!0:null!=t?N(e):u.add(e)}g.prevProp=y,g.prevResolvedValues=T,g.isActive&&(c={...c,...T}),s&&e.blockInitialAnimation&&(j=!1);let A=!(b&&w)||k;j&&A&&d.push(...P.map(e=>({animation:e,options:{type:h}})))}if(u.size){let t={};if("boolean"!=typeof a.initial){let i=h(e,Array.isArray(a.initial)?a.initial[0]:a.initial);i&&i.transition&&(t.transition=i.transition)}u.forEach(i=>{let s=e.getBaseTarget(i),r=e.getValue(i);r&&(r.liveStyle=!0),t[i]=s??null}),d.push({animation:t})}let g=!!d.length;return s&&(!1===a.initial||a.initial===a.animate)&&!e.manuallyAnimateOnMount&&(g=!1),s=!1,g?t(d):Promise.resolve()}return{animateChanges:n,setActive:function(t,s){if(i[t].isActive===s)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,s)),i[t].isActive=s;let r=n(t);for(let e in i)i[e].protectedKeys={};return r},setAnimateFunction:function(i){t=i(e)},getState:()=>i,reset:()=>{i=ic(),s=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();l(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ig=0;class iy extends im{constructor(){super(...arguments),this.id=ig++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;let s=this.node.animationState.setActive("exit",!e);t&&!e&&s.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let ix={x:!1,y:!1};function iv(e,t,i,s={passive:!0}){return e.addEventListener(t,i,s),()=>e.removeEventListener(t,i)}let ib=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function iw(e){return{point:{x:e.pageX,y:e.pageY}}}let ij=e=>t=>ib(t)&&e(t,iw(t));function ik(e,t,i,s){return iv(e,t,ij(i),s)}function iP({top:e,left:t,right:i,bottom:s}){return{x:{min:t,max:i},y:{min:e,max:s}}}function iT(e){return e.max-e.min}function iS(e,t,i,s=.5){e.origin=s,e.originPoint=eC(t.min,t.max,e.origin),e.scale=iT(i)/iT(t),e.translate=eC(i.min,i.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function iM(e,t,i,s){iS(e.x,t.x,i.x,s?s.originX:void 0),iS(e.y,t.y,i.y,s?s.originY:void 0)}function iN(e,t,i){e.min=i.min+t.min,e.max=e.min+iT(t)}function iA(e,t,i){e.min=t.min-i.min,e.max=e.min+iT(t)}function iC(e,t,i){iA(e.x,t.x,i.x),iA(e.y,t.y,i.y)}let iE=()=>({translate:0,scale:1,origin:0,originPoint:0}),iV=()=>({x:iE(),y:iE()}),iD=()=>({min:0,max:0}),iR=()=>({x:iD(),y:iD()});function iL(e){return[e("x"),e("y")]}function iF(e){return void 0===e||1===e}function iB({scale:e,scaleX:t,scaleY:i}){return!iF(e)||!iF(t)||!iF(i)}function iz(e){return iB(e)||iO(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function iO(e){var t,i;return(t=e.x)&&"0%"!==t||(i=e.y)&&"0%"!==i}function iI(e,t,i,s,r){return void 0!==r&&(e=s+r*(e-s)),s+i*(e-s)+t}function i$(e,t=0,i=1,s,r){e.min=iI(e.min,t,i,s,r),e.max=iI(e.max,t,i,s,r)}function iU(e,{x:t,y:i}){i$(e.x,t.translate,t.scale,t.originPoint),i$(e.y,i.translate,i.scale,i.originPoint)}function iW(e,t){e.min=e.min+t,e.max=e.max+t}function iq(e,t,i,s,r=.5){let n=eC(e.min,e.max,r);i$(e,t,i,n,s)}function iH(e,t){iq(e.x,t.x,t.scaleX,t.scale,t.originX),iq(e.y,t.y,t.scaleY,t.scale,t.originY)}function iG(e,t){return iP(function(e,t){if(!t)return e;let i=t({x:e.left,y:e.top}),s=t({x:e.right,y:e.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(e.getBoundingClientRect(),t))}let iY=({current:e})=>e?e.ownerDocument.defaultView:null;function iX(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let i_=(e,t)=>Math.abs(e-t);class iK{constructor(e,t,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=iJ(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,i=function(e,t){return Math.sqrt(i_(e.x,t.x)**2+i_(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!i)return;let{point:s}=e,{timestamp:r}=b;this.history.push({...s,timestamp:r});let{onStart:n,onMove:a}=this.handlers;t||(n&&n(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=iZ(t,this.transformPagePoint),x.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let n=iJ("pointercancel"===e.type?this.lastMoveEventInfo:iZ(t,this.transformPagePoint),this.history);this.startEvent&&i&&i(e,n),s&&s(e,n)},!ib(e))return;this.dragSnapToOrigin=r,this.handlers=t,this.transformPagePoint=i,this.contextWindow=s||window;let n=iZ(iw(e),this.transformPagePoint),{point:a}=n,{timestamp:o}=b;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=t;l&&l(e,iJ(n,this.history)),this.removeListeners=I(ik(this.contextWindow,"pointermove",this.handlePointerMove),ik(this.contextWindow,"pointerup",this.handlePointerUp),ik(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),v(this.updatePoint)}}function iZ(e,t){return t?{point:t(e.point)}:e}function iQ(e,t){return{x:e.x-t.x,y:e.y-t.y}}function iJ({point:e},t){return{point:e,delta:iQ(e,i0(t)),offset:iQ(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let i=e.length-1,s=null,r=i0(e);for(;i>=0&&(s=e[i],!(r.timestamp-s.timestamp>U(.1)));)i--;if(!s)return{x:0,y:0};let n=W(r.timestamp-s.timestamp);if(0===n)return{x:0,y:0};let a={x:(r.x-s.x)/n,y:(r.y-s.y)/n};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,.1)}}function i0(e){return e[e.length-1]}function i1(e,t,i){return{min:void 0!==t?e.min+t:void 0,max:void 0!==i?e.max+i-(e.max-e.min):void 0}}function i2(e,t){let i=t.min-e.min,s=t.max-e.max;return t.max-t.min<e.max-e.min&&([i,s]=[s,i]),{min:i,max:s}}function i5(e,t,i){return{min:i6(e,t),max:i6(e,i)}}function i6(e,t){return"number"==typeof e?e:e[t]||0}let i3=new WeakMap;class i4{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iR(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new iK(e,{onSessionStart:e=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(iw(e).point)},onStart:(e,t)=>{let{drag:i,dragPropagation:s,onDragStart:r}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(ix[e])return null;else return ix[e]=!0,()=>{ix[e]=!1};return ix.x||ix.y?null:(ix.x=ix.y=!0,()=>{ix.x=ix.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iL(e=>{let t=this.getAxisMotionValue(e).get()||0;if(ec.test(t)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[e];s&&(t=iT(s)*(parseFloat(t)/100))}}this.originPoint[e]=t}),r&&x.postRender(()=>r(e,t)),F(this.visualElement,"transform");let{animationState:n}=this.visualElement;n&&n.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:r,onDrag:n}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=t;if(s&&null===this.currentDirection){this.currentDirection=function(e,t=10){let i=null;return Math.abs(e.y)>t?i="y":Math.abs(e.x)>t&&(i="x"),i}(a),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),n&&n(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>iL(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:iY(this.visualElement)})}stop(e,t){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=t;this.startAnimation(s);let{onDragEnd:r}=this.getProps();r&&x.postRender(()=>r(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,i){let{drag:s}=this.getProps();if(!i||!i8(e,s,this.currentDirection))return;let r=this.getAxisMotionValue(e),n=this.originPoint[e]+i[e];this.constraints&&this.constraints[e]&&(n=function(e,{min:t,max:i},s){return void 0!==t&&e<t?e=s?eC(t,e,s.min):Math.max(e,t):void 0!==i&&e>i&&(e=s?eC(i,e,s.max):Math.min(e,i)),e}(n,this.constraints[e],this.elastic[e])),r.set(n)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;e&&iX(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&i?this.constraints=function(e,{top:t,left:i,bottom:s,right:r}){return{x:i1(e.x,i,r),y:i1(e.y,t,s)}}(i.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:i5(e,"left","right"),y:i5(e,"top","bottom")}}(t),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iL(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let i={};return void 0!==t.min&&(i.min=t.min-e.min),void 0!==t.max&&(i.max=t.max-e.min),i}(i.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:i}=this.getProps();if(!t||!iX(t))return!1;let s=t.current;G(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let n=function(e,t,i){let s=iG(e,i),{scroll:r}=t;return r&&(iW(s.x,r.offset.x),iW(s.y,r.offset.y)),s}(s,r.root,this.visualElement.getTransformPagePoint()),a=(e=r.layout.layoutBox,{x:i2(e.x,n.x),y:i2(e.y,n.y)});if(i){let e=i(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=iP(e))}return a}startAnimation(e){let{drag:t,dragMomentum:i,dragElastic:s,dragTransition:r,dragSnapToOrigin:n,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(iL(a=>{if(!i8(a,t,this.currentDirection))return;let l=o&&o[a]||{};n&&(l={min:0,max:0});let d={type:"inertia",velocity:i?e[a]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(a,d)})).then(a)}startAxisValueAnimation(e,t){let i=this.getAxisMotionValue(e);return F(this.visualElement,e),i.start(t7(e,i,0,t,this.visualElement,!1))}stopAnimation(){iL(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){iL(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,i=this.visualElement.getProps();return i[t]||this.visualElement.getValue(e,(i.initial?i.initial[e]:void 0)||0)}snapToCursor(e){iL(t=>{let{drag:i}=this.getProps();if(!i8(t,i,this.currentDirection))return;let{projection:s}=this.visualElement,r=this.getAxisMotionValue(t);if(s&&s.layout){let{min:i,max:n}=s.layout.layoutBox[t];r.set(e[t]-eC(i,n,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:i}=this.visualElement;if(!iX(t)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};iL(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let i=t.get();s[e]=function(e,t){let i=.5,s=iT(e),r=iT(t);return r>s?i=td(t.min,t.max-s,e.min):s>r&&(i=td(e.min,e.max-r,t.min)),$(0,1,i)}({min:i,max:i},this.constraints[e])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iL(t=>{if(!i8(t,e,null))return;let i=this.getAxisMotionValue(t),{min:r,max:n}=this.constraints[t];i.set(eC(r,n,s[t]))})}addListeners(){if(!this.visualElement.current)return;i3.set(this.visualElement,this);let e=ik(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:i=!0}=this.getProps();t&&i&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();iX(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",t);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),x.read(t);let r=iv(window,"resize",()=>this.scalePositionWithinConstraints()),n=i.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(iL(t=>{let i=this.getAxisMotionValue(t);i&&(this.originPoint[t]+=e[t].translate,i.set(i.get()+e[t].translate))}),this.visualElement.render())});return()=>{r(),e(),s(),n&&n()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:r=!1,dragElastic:n=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:i,dragPropagation:s,dragConstraints:r,dragElastic:n,dragMomentum:a}}}function i8(e,t,i){return(!0===t||t===e)&&(null===i||i===e)}class i9 extends im{constructor(e){super(e),this.removeGroupControls=m,this.removeListeners=m,this.controls=new i4(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||m}unmount(){this.removeGroupControls(),this.removeListeners()}}let i7=e=>(t,i)=>{e&&x.postRender(()=>e(t,i))};class se extends im{constructor(){super(...arguments),this.removePointerDownListener=m}onPointerDown(e){this.session=new iK(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iY(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:i7(e),onStart:i7(t),onMove:i,onEnd:(e,t)=>{delete this.session,s&&x.postRender(()=>s(e,t))}}}mount(){this.removePointerDownListener=ik(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let{schedule:st}=y(queueMicrotask,!1),si=(0,o.createContext)(null);function ss(e=!0){let t=(0,o.useContext)(si);if(null===t)return[!0,null];let{isPresent:i,onExitComplete:s,register:r}=t,n=(0,o.useId)();(0,o.useEffect)(()=>{if(e)return r(n)},[e]);let a=(0,o.useCallback)(()=>e&&s&&s(n),[n,s,e]);return!i&&s?[!1,a]:[!0]}let sr=(0,o.createContext)({}),sn=(0,o.createContext)({}),sa={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function so(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let sl={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!em.test(e))return e;else e=parseFloat(e);let i=so(e,t.target.x),s=so(e,t.target.y);return`${i}% ${s}%`}},sd={};class su extends o.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i,layoutId:s}=this.props,{projection:r}=e;for(let e in sc)sd[e]=sc[e],X(e)&&(sd[e].isCSSVariable=!0);r&&(t.group&&t.group.add(r),i&&i.register&&s&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),sa.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:i,drag:s,isPresent:r}=this.props,{projection:n}=i;return n&&(n.isPresent=r,s||e.layoutDependency!==t||void 0===t||e.isPresent!==r?n.willUpdate():this.safeToRemove(),e.isPresent!==r&&(r?n.promote():n.relegate()||x.postRender(()=>{let e=n.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),st.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i}=this.props,{projection:s}=e;s&&(s.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function sh(e){let[t,i]=ss(),s=(0,o.useContext)(sr);return(0,a.jsx)(su,{...e,layoutGroup:s,switchLayoutGroup:(0,o.useContext)(sn),isPresent:t,safeToRemove:i})}let sc={borderRadius:{...sl,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:sl,borderTopRightRadius:sl,borderBottomLeftRadius:sl,borderBottomRightRadius:sl,boxShadow:{correct:(e,{treeScale:t,projectionDelta:i})=>{let s=eM.parse(e);if(s.length>5)return e;let r=eM.createTransformer(e),n=+("number"!=typeof s[0]),a=i.x.scale*t.x,o=i.y.scale*t.y;s[0+n]/=a,s[1+n]/=o;let l=eC(a,o,.5);return"number"==typeof s[2+n]&&(s[2+n]/=l),"number"==typeof s[3+n]&&(s[3+n]/=l),r(s)}}};function sm(e){return tQ(e)&&"ownerSVGElement"in e}let sp=(e,t)=>e.depth-t.depth;class sf{constructor(){this.children=[],this.isDirty=!1}add(e){T(this.children,e),this.isDirty=!0}remove(e){S(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(sp),this.isDirty=!1,this.children.forEach(e)}}function sg(e){return L(e)?e.get():e}let sy=["TopLeft","TopRight","BottomLeft","BottomRight"],sx=sy.length,sv=e=>"string"==typeof e?parseFloat(e):e,sb=e=>"number"==typeof e||em.test(e);function sw(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let sj=sP(0,.5,ts),sk=sP(.5,.95,m);function sP(e,t,i){return s=>s<e?0:s>t?1:i(td(e,t,s))}function sT(e,t){e.min=t.min,e.max=t.max}function sS(e,t){sT(e.x,t.x),sT(e.y,t.y)}function sM(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function sN(e,t,i,s,r){return e-=t,e=s+1/i*(e-s),void 0!==r&&(e=s+1/r*(e-s)),e}function sA(e,t,[i,s,r],n,a){!function(e,t=0,i=1,s=.5,r,n=e,a=e){if(ec.test(t)&&(t=parseFloat(t),t=eC(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let o=eC(n.min,n.max,s);e===n&&(o-=t),e.min=sN(e.min,t,i,o,r),e.max=sN(e.max,t,i,o,r)}(e,t[i],t[s],t[r],t.scale,n,a)}let sC=["x","scaleX","originX"],sE=["y","scaleY","originY"];function sV(e,t,i,s){sA(e.x,t,sC,i?i.x:void 0,s?s.x:void 0),sA(e.y,t,sE,i?i.y:void 0,s?s.y:void 0)}function sD(e){return 0===e.translate&&1===e.scale}function sR(e){return sD(e.x)&&sD(e.y)}function sL(e,t){return e.min===t.min&&e.max===t.max}function sF(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function sB(e,t){return sF(e.x,t.x)&&sF(e.y,t.y)}function sz(e){return iT(e.x)/iT(e.y)}function sO(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class sI{constructor(){this.members=[]}add(e){T(this.members,e),e.scheduleRender()}remove(e){if(S(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,i=this.members.findIndex(t=>e===t);if(0===i)return!1;for(let e=i;e>=0;e--){let i=this.members[e];if(!1!==i.isPresent){t=i;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,t&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:s}=e.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:i}=e;t.onExitComplete&&t.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let s$={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},sU=["","X","Y","Z"],sW={visibility:"hidden"},sq=0;function sH(e,t,i,s){let{latestValues:r}=t;r[e]&&(i[e]=r[e],t.setStaticValue(e,0),s&&(s[e]=0))}function sG({attachResizeListener:e,defaultParent:t,measureScroll:i,checkIsScrollRoot:s,resetTransform:r}){return class{constructor(e={},i=t?.()){this.id=sq++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,g.value&&(s$.nodes=s$.calculatedTargetDeltas=s$.calculatedProjections=0),this.nodes.forEach(s_),this.nodes.forEach(s2),this.nodes.forEach(s5),this.nodes.forEach(sK),g.addProjectionMetrics&&g.addProjectionMetrics(s$)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new sf)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new M),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let i=this.eventHandlers.get(e);i&&i.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=sm(t)&&!(sm(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:i,layout:s,visualElement:r}=this.options;if(r&&!r.current&&r.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),e){let i,s=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(e,t){let i=A.now(),s=({timestamp:r})=>{let n=r-i;n>=250&&(v(s),e(n-t))};return x.setup(s,!0),()=>v(s)}(s,250),sa.hasAnimatedSinceResize&&(sa.hasAnimatedSinceResize=!1,this.nodes.forEach(s1))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||s)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||r.getDefaultTransition()||s7,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=r.getProps(),l=!this.targetLayout||!sB(this.targetLayout,s),d=!t&&i;if(this.options.layoutRoot||this.resumeFrom||d||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...c(n,"layout"),onPlay:a,onComplete:o};(r.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,d)}else t||s1(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),v(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(s6),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:i}=t.options;if(!i)return;let s=i.props[z];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(s,"transform",x,!(e||i))}let{parent:r}=t;r&&!r.hasCheckedOptimisedAppear&&e(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:i}=this.options;if(void 0===t&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(sQ);return}this.isUpdating||this.nodes.forEach(sJ),this.isUpdating=!1,this.nodes.forEach(s0),this.nodes.forEach(sY),this.nodes.forEach(sX),this.clearAllSnapshots();let e=A.now();b.delta=$(0,1e3/60,e-b.timestamp),b.timestamp=e,b.isProcessing=!0,w.update.process(b),w.preRender.process(b),w.render.process(b),b.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,st.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(sZ),this.sharedNodes.forEach(s3)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,x.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){x.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iT(this.snapshot.measuredBox.x)||iT(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iR(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=s(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!r)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!sR(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,n=s!==this.prevTransformTemplateValue;e&&this.instance&&(t||iz(this.latestValues)||n)&&(r(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let i=this.measurePageBox(),s=this.removeElementScroll(i);return e&&(s=this.removeTransform(s)),ri((t=s).x),ri(t.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return iR();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rr))){let{scroll:e}=this.root;e&&(iW(t.x,e.offset.x),iW(t.y,e.offset.y))}return t}removeElementScroll(e){let t=iR();if(sS(t,e),this.scroll?.wasRoot)return t;for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:r,options:n}=s;s!==this.root&&r&&n.layoutScroll&&(r.wasRoot&&sS(t,e),iW(t.x,r.offset.x),iW(t.y,r.offset.y))}return t}applyTransform(e,t=!1){let i=iR();sS(i,e);for(let e=0;e<this.path.length;e++){let s=this.path[e];!t&&s.options.layoutScroll&&s.scroll&&s!==s.root&&iH(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),iz(s.latestValues)&&iH(i,s.latestValues)}return iz(this.latestValues)&&iH(i,this.latestValues),i}removeTransform(e){let t=iR();sS(t,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];if(!i.instance||!iz(i.latestValues))continue;iB(i.latestValues)&&i.updateSnapshot();let s=iR();sS(s,i.measurePageBox()),sV(t,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return iz(this.latestValues)&&sV(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==b.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==t;if(!(e||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:r}=this.options;if(this.layout&&(s||r)){if(this.resolvedRelativeTargetAt=b.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iR(),this.relativeTargetOrigin=iR(),iC(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),sS(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iR(),this.targetWithTransforms=iR()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var n,a,o;this.forceRelativeParentToResolveTarget(),n=this.target,a=this.relativeTarget,o=this.relativeParent.target,iN(n.x,a.x,o.x),iN(n.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sS(this.target,this.layout.layoutBox),iU(this.target,this.targetDelta)):sS(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iR(),this.relativeTargetOrigin=iR(),iC(this.relativeTargetOrigin,this.target,e.target),sS(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}g.value&&s$.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iB(this.parent.latestValues)||iO(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===b.timestamp&&(i=!1),i)return;let{layout:s,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||r))return;sS(this.layoutCorrected,this.layout.layoutBox);let n=this.treeScale.x,a=this.treeScale.y;!function(e,t,i,s=!1){let r,n,a=i.length;if(a){t.x=t.y=1;for(let o=0;o<a;o++){n=(r=i[o]).projectionDelta;let{visualElement:a}=r.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(s&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iH(e,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),n&&(t.x*=n.x.scale,t.y*=n.y.scale,iU(e,n)),s&&iz(r.latestValues)&&iH(e,r.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=iR());let{target:o}=e;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(sM(this.prevProjectionDelta.x,this.projectionDelta.x),sM(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iM(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===n&&this.treeScale.y===a&&sO(this.projectionDelta.x,this.prevProjectionDelta.x)&&sO(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),g.value&&s$.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iV(),this.projectionDelta=iV(),this.projectionDeltaWithTransform=iV()}setAnimationOrigin(e,t=!1){let i,s=this.snapshot,r=s?s.latestValues:{},n={...this.latestValues},a=iV();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=iR(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),d=this.getStack(),u=!d||d.members.length<=1,h=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(s9));this.animationProgress=0,this.mixTargetDelta=t=>{let s=t/1e3;if(s4(a.x,e.x,s),s4(a.y,e.y,s),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var d,c,m,p,f,g;iC(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),m=this.relativeTarget,p=this.relativeTargetOrigin,f=o,g=s,s8(m.x,p.x,f.x,g),s8(m.y,p.y,f.y,g),i&&(d=this.relativeTarget,c=i,sL(d.x,c.x)&&sL(d.y,c.y))&&(this.isProjectionDirty=!1),i||(i=iR()),sS(i,this.relativeTarget)}l&&(this.animationValues=n,function(e,t,i,s,r,n){r?(e.opacity=eC(0,i.opacity??1,sj(s)),e.opacityExit=eC(t.opacity??1,0,sk(s))):n&&(e.opacity=eC(t.opacity??1,i.opacity??1,s));for(let r=0;r<sx;r++){let n=`border${sy[r]}Radius`,a=sw(t,n),o=sw(i,n);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||sb(a)===sb(o)?(e[n]=Math.max(eC(sv(a),sv(o),s),0),(ec.test(o)||ec.test(a))&&(e[n]+="%")):e[n]=o)}(t.rotate||i.rotate)&&(e.rotate=eC(t.rotate||0,i.rotate||0,s))}(n,r,this.latestValues,s,h,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(v(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=x.update(()=>{sa.hasAnimatedSinceResize=!0,q.layout++,this.motionValue||(this.motionValue=D(0)),this.currentAnimation=function(e,t,i){let s=L(e)?e:D(e);return s.start(t7("",s,t,i)),s.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{q.layout--},onComplete:()=>{q.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:i,layout:s,latestValues:r}=e;if(t&&i&&s){if(this!==e&&this.layout&&s&&rs(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||iR();let t=iT(this.layout.layoutBox.x);i.x.min=e.target.x.min,i.x.max=i.x.min+t;let s=iT(this.layout.layoutBox.y);i.y.min=e.target.y.min,i.y.max=i.y.min+s}sS(t,i),iH(t,r),iM(this.projectionDeltaWithTransform,this.layoutCorrected,t,r)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new sI),this.sharedNodes.get(e).add(t);let i=t.options.initialPromotionConfig;t.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:i}=e;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(t=!0),!t)return;let s={};i.z&&sH("z",e,s,this.animationValues);for(let t=0;t<sU.length;t++)sH(`rotate${sU[t]}`,e,s,this.animationValues),sH(`skew${sU[t]}`,e,s,this.animationValues);for(let t in e.render(),s)e.setStaticValue(t,s[t]),this.animationValues&&(this.animationValues[t]=s[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return sW;let t={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=sg(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none",t;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=sg(e?.pointerEvents)||""),this.hasProjected&&!iz(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}let r=s.animationValues||s.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,i){let s="",r=e.x.translate/t.x,n=e.y.translate/t.y,a=i?.z||0;if((r||n||a)&&(s=`translate3d(${r}px, ${n}px, ${a}px) `),(1!==t.x||1!==t.y)&&(s+=`scale(${1/t.x}, ${1/t.y}) `),i){let{transformPerspective:e,rotate:t,rotateX:r,rotateY:n,skewX:a,skewY:o}=i;e&&(s=`perspective(${e}px) ${s}`),t&&(s+=`rotate(${t}deg) `),r&&(s+=`rotateX(${r}deg) `),n&&(s+=`rotateY(${n}deg) `),a&&(s+=`skewX(${a}deg) `),o&&(s+=`skewY(${o}deg) `)}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(s+=`scale(${o}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),i&&(t.transform=i(r,t.transform));let{x:n,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*n.origin}% ${100*a.origin}% 0`,s.animationValues?t.opacity=s===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:t.opacity=s===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,sd){if(void 0===r[e])continue;let{correct:i,applyTo:n,isCSSVariable:a}=sd[e],o="none"===t.transform?r[e]:i(r[e],s);if(n){let e=n.length;for(let i=0;i<e;i++)t[n[i]]=o}else a?this.options.visualElement.renderState.vars[e]=o:t[e]=o}return this.options.layoutId&&(t.pointerEvents=s===this?sg(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(sQ),this.root.sharedNodes.clear()}}}function sY(e){e.updateLayout()}function sX(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:s}=e.layout,{animationType:r}=e.options,n=t.source!==e.layout.source;"size"===r?iL(e=>{let s=n?t.measuredBox[e]:t.layoutBox[e],r=iT(s);s.min=i[e].min,s.max=s.min+r}):rs(r,t.layoutBox,i)&&iL(s=>{let r=n?t.measuredBox[s]:t.layoutBox[s],a=iT(i[s]);r.max=r.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[s].max=e.relativeTarget[s].min+a)});let a=iV();iM(a,i,t.layoutBox);let o=iV();n?iM(o,e.applyTransform(s,!0),t.measuredBox):iM(o,i,t.layoutBox);let l=!sR(a),d=!1;if(!e.resumeFrom){let s=e.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:r,layout:n}=s;if(r&&n){let a=iR();iC(a,t.layoutBox,r.layoutBox);let o=iR();iC(o,i,n.layoutBox),sB(a,o)||(d=!0),s.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=a,e.relativeParent=s)}}}e.notifyListeners("didUpdate",{layout:i,snapshot:t,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:d})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function s_(e){g.value&&s$.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function sK(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function sZ(e){e.clearSnapshot()}function sQ(e){e.clearMeasurements()}function sJ(e){e.isLayoutDirty=!1}function s0(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function s1(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function s2(e){e.resolveTargetDelta()}function s5(e){e.calcProjection()}function s6(e){e.resetSkewAndRotation()}function s3(e){e.removeLeadSnapshot()}function s4(e,t,i){e.translate=eC(t.translate,0,i),e.scale=eC(t.scale,1,i),e.origin=t.origin,e.originPoint=t.originPoint}function s8(e,t,i,s){e.min=eC(t.min,i.min,s),e.max=eC(t.max,i.max,s)}function s9(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let s7={duration:.45,ease:[.4,0,.1,1]},re=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),rt=re("applewebkit/")&&!re("chrome/")?Math.round:m;function ri(e){e.min=rt(e.min),e.max=rt(e.max)}function rs(e,t,i){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(sz(t)-sz(i)))}function rr(e){return e!==e.root&&e.scroll?.wasRoot}let rn=sG({attachResizeListener:(e,t)=>iv(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ra={current:void 0},ro=sG({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ra.current){let e=new rn({});e.mount(window),e.setOptions({layoutScroll:!0}),ra.current=e}return ra.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function rl(e,t){let i=function(e,t,i){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,i=(void 0)??t.querySelectorAll(e);return i?Array.from(i):[]}return Array.from(e)}(e),s=new AbortController;return[i,{passive:!0,...t,signal:s.signal},()=>s.abort()]}function rd(e){return!("touch"===e.pointerType||ix.x||ix.y)}function ru(e,t,i){let{props:s}=e;e.animationState&&s.whileHover&&e.animationState.setActive("whileHover","Start"===i);let r=s["onHover"+i];r&&x.postRender(()=>r(t,iw(t)))}class rh extends im{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[s,r,n]=rl(e,i),a=e=>{if(!rd(e))return;let{target:i}=e,s=t(i,e);if("function"!=typeof s||!i)return;let n=e=>{rd(e)&&(s(e),i.removeEventListener("pointerleave",n))};i.addEventListener("pointerleave",n,r)};return s.forEach(e=>{e.addEventListener("pointerenter",a,r)}),n}(e,(e,t)=>(ru(this.node,t,"Start"),e=>ru(this.node,e,"End"))))}unmount(){}}class rc extends im{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=I(iv(this.node.current,"focus",()=>this.onFocus()),iv(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rm=(e,t)=>!!t&&(e===t||rm(e,t.parentElement)),rp=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rf=new WeakSet;function rg(e){return t=>{"Enter"===t.key&&e(t)}}function ry(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let rx=(e,t)=>{let i=e.currentTarget;if(!i)return;let s=rg(()=>{if(rf.has(i))return;ry(i,"down");let e=rg(()=>{ry(i,"up")});i.addEventListener("keyup",e,t),i.addEventListener("blur",()=>ry(i,"cancel"),t)});i.addEventListener("keydown",s,t),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),t)};function rv(e){return ib(e)&&!(ix.x||ix.y)}function rb(e,t,i){let{props:s}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&s.whileTap&&e.animationState.setActive("whileTap","Start"===i);let r=s["onTap"+("End"===i?"":i)];r&&x.postRender(()=>r(t,iw(t)))}class rw extends im{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[s,r,n]=rl(e,i),a=e=>{let s=e.currentTarget;if(!rv(e))return;rf.add(s);let n=t(s,e),a=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),rf.has(s)&&rf.delete(s),rv(e)&&"function"==typeof n&&n(e,{success:t})},o=e=>{a(e,s===window||s===document||i.useGlobalTarget||rm(s,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",o,r),window.addEventListener("pointercancel",l,r)};return s.forEach(e=>{((i.useGlobalTarget?window:e).addEventListener("pointerdown",a,r),tJ(e))&&(e.addEventListener("focus",e=>rx(e,r)),rp.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),n}(e,(e,t)=>(rb(this.node,t,"Start"),(e,{success:t})=>rb(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rj=new WeakMap,rk=new WeakMap,rP=e=>{let t=rj.get(e.target);t&&t(e)},rT=e=>{e.forEach(rP)},rS={some:0,all:1};class rM extends im{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:i,amount:s="some",once:r}=e,n={root:t?t.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:rS[s]};return function(e,t,i){let s=function({root:e,...t}){let i=e||document;rk.has(i)||rk.set(i,{});let s=rk.get(i),r=JSON.stringify(t);return s[r]||(s[r]=new IntersectionObserver(rT,{root:e,...t})),s[r]}(t);return rj.set(e,i),s.observe(e),()=>{rj.delete(e),s.unobserve(e)}}(this.node.current,n,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,r&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),n=t?i:s;n&&n(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return i=>e[i]!==t[i]}(e,t))&&this.startObserver()}unmount(){}}let rN=(0,o.createContext)({strict:!1}),rA=(0,o.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),rC=(0,o.createContext)({});function rE(e){return l(e.animate)||io.some(t=>ir(e[t]))}function rV(e){return!!(rE(e)||e.variants)}function rD(e){return Array.isArray(e)?e.join(" "):e}let rR="undefined"!=typeof window,rL={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rF={};for(let e in rL)rF[e]={isEnabled:t=>rL[e].some(e=>!!t[e])};let rB=Symbol.for("motionComponentSymbol"),rz=rR?o.useLayoutEffect:o.useEffect;function rO(e,{layout:t,layoutId:i}){return k.has(e)||e.startsWith("origin")||(t||void 0!==i)&&(!!sd[e]||"opacity"===e)}let rI=(e,t)=>t&&"number"==typeof e?t.transform(e):e,r$={...Q,transform:Math.round},rU={borderWidth:em,borderTopWidth:em,borderRightWidth:em,borderBottomWidth:em,borderLeftWidth:em,borderRadius:em,radius:em,borderTopLeftRadius:em,borderTopRightRadius:em,borderBottomRightRadius:em,borderBottomLeftRadius:em,width:em,maxWidth:em,height:em,maxHeight:em,top:em,right:em,bottom:em,left:em,padding:em,paddingTop:em,paddingRight:em,paddingBottom:em,paddingLeft:em,margin:em,marginTop:em,marginRight:em,marginBottom:em,marginLeft:em,backgroundPositionX:em,backgroundPositionY:em,rotate:eh,rotateX:eh,rotateY:eh,rotateZ:eh,scale:ee,scaleX:ee,scaleY:ee,scaleZ:ee,skew:eh,skewX:eh,skewY:eh,distance:em,translateX:em,translateY:em,translateZ:em,x:em,y:em,z:em,perspective:em,transformPerspective:em,opacity:J,originX:eg,originY:eg,originZ:em,zIndex:r$,fillOpacity:J,strokeOpacity:J,numOctaves:r$},rW={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rq=j.length;function rH(e,t,i){let{style:s,vars:r,transformOrigin:n}=e,a=!1,o=!1;for(let e in t){let i=t[e];if(k.has(e)){a=!0;continue}if(X(e)){r[e]=i;continue}{let t=rI(i,rU[e]);e.startsWith("origin")?(o=!0,n[e]=t):s[e]=t}}if(!t.transform&&(a||i?s.transform=function(e,t,i){let s="",r=!0;for(let n=0;n<rq;n++){let a=j[n],o=e[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let e=rI(o,rU[a]);if(!l){r=!1;let t=rW[a]||a;s+=`${t}(${e}) `}i&&(t[a]=e)}}return s=s.trim(),i?s=i(t,r?"":s):r&&(s="none"),s}(t,e.transform,i):s.transform&&(s.transform="none")),o){let{originX:e="50%",originY:t="50%",originZ:i=0}=n;s.transformOrigin=`${e} ${t} ${i}`}}let rG=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rY(e,t,i){for(let s in t)L(t[s])||rO(s,i)||(e[s]=t[s])}let rX={offset:"stroke-dashoffset",array:"stroke-dasharray"},r_={offset:"strokeDashoffset",array:"strokeDasharray"};function rK(e,{attrX:t,attrY:i,attrScale:s,pathLength:r,pathSpacing:n=1,pathOffset:a=0,...o},l,d,u){if(rH(e,o,d),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:h,style:c}=e;h.transform&&(c.transform=h.transform,delete h.transform),(c.transform||h.transformOrigin)&&(c.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),c.transform&&(c.transformBox=u?.transformBox??"fill-box",delete h.transformBox),void 0!==t&&(h.x=t),void 0!==i&&(h.y=i),void 0!==s&&(h.scale=s),void 0!==r&&function(e,t,i=1,s=0,r=!0){e.pathLength=1;let n=r?rX:r_;e[n.offset]=em.transform(-s);let a=em.transform(t),o=em.transform(i);e[n.array]=`${a} ${o}`}(h,r,n,a,!1)}let rZ=()=>({...rG(),attrs:{}}),rQ=e=>"string"==typeof e&&"svg"===e.toLowerCase(),rJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function r0(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||rJ.has(e)}let r1=e=>!r0(e);try{!function(e){e&&(r1=t=>t.startsWith("on")?!r0(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let r2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r5(e){if("string"!=typeof e||e.includes("-"));else if(r2.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}function r6(e){let t=(0,o.useRef)(null);return null===t.current&&(t.current=e()),t.current}let r3=e=>(t,i)=>{let s=(0,o.useContext)(rC),r=(0,o.useContext)(si),n=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},i,s,r){return{latestValues:function(e,t,i,s){let r={},n=s(e,{});for(let e in n)r[e]=sg(n[e]);let{initial:a,animate:o}=e,d=rE(e),h=rV(e);t&&h&&!d&&!1!==e.inherit&&(void 0===a&&(a=t.initial),void 0===o&&(o=t.animate));let c=!!i&&!1===i.initial,m=(c=c||!1===a)?o:a;if(m&&"boolean"!=typeof m&&!l(m)){let t=Array.isArray(m)?m:[m];for(let i=0;i<t.length;i++){let s=u(e,t[i]);if(s){let{transitionEnd:e,transition:t,...i}=s;for(let e in i){let t=i[e];if(Array.isArray(t)){let e=c?t.length-1:0;t=t[e]}null!==t&&(r[e]=t)}for(let t in e)r[t]=e[t]}}}return r}(i,s,r,e),renderState:t()}})(e,t,s,r);return i?n():r6(n)};function r4(e,t,i){let{style:s}=e,r={};for(let n in s)(L(s[n])||t.style&&L(t.style[n])||rO(n,e)||i?.getValue(n)?.liveStyle!==void 0)&&(r[n]=s[n]);return r}let r8={useVisualState:r3({scrapeMotionValuesFromProps:r4,createRenderState:rG})};function r9(e,t,i){let s=r4(e,t,i);for(let i in e)(L(e[i])||L(t[i]))&&(s[-1!==j.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]);return s}let r7={useVisualState:r3({scrapeMotionValuesFromProps:r9,createRenderState:rZ})},ne=e=>t=>t.test(e),nt=[Q,em,ec,eh,ef,ep,{test:e=>"auto"===e,parse:e=>e}],ni=e=>nt.find(ne(e)),ns=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),nr=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,nn=e=>/^0[^.\s]+$/u.test(e),na=new Set(["brightness","contrast","saturate","opacity"]);function no(e){let[t,i]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[s]=i.match(ei)||[];if(!s)return e;let r=i.replace(s,""),n=+!!na.has(t);return s!==i&&(n*=100),t+"("+n+r+")"}let nl=/\b([a-z-]*)\(.*?\)/gu,nd={...eM,getAnimatableNone:e=>{let t=e.match(nl);return t?t.map(no).join(" "):e}},nu={...rU,color:ex,backgroundColor:ex,outlineColor:ex,fill:ex,stroke:ex,borderColor:ex,borderTopColor:ex,borderRightColor:ex,borderBottomColor:ex,borderLeftColor:ex,filter:nd,WebkitFilter:nd},nh=e=>nu[e];function nc(e,t){let i=nh(e);return i!==nd&&(i=eM),i.getAnimatableNone?i.getAnimatableNone(t):void 0}let nm=new Set(["auto","none","0"]);class np extends tO{constructor(e,t,i,s,r){super(e,t,i,s,r,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:i}=this;if(!t||!t.current)return;super.readKeyframes();for(let i=0;i<e.length;i++){let s=e[i];if("string"==typeof s&&K(s=s.trim())){let r=function e(t,i,s=1){G(s<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[r,n]=function(e){let t=nr.exec(e);if(!t)return[,];let[,i,s,r]=t;return[`--${i??s}`,r]}(t);if(!r)return;let a=window.getComputedStyle(i).getPropertyValue(r);if(a){let e=a.trim();return ns(e)?parseFloat(e):e}return K(n)?e(n,i,s+1):n}(s,t.current);void 0!==r&&(e[i]=r),i===e.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!P.has(i)||2!==e.length)return;let[s,r]=e,n=ni(s),a=ni(r);if(n!==a)if(tA(n)&&tA(a))for(let t=0;t<e.length;t++){let i=e[t];"string"==typeof i&&(e[t]=parseFloat(i))}else tV[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,i=[];for(let t=0;t<e.length;t++){var s;(null===e[t]||("number"==typeof(s=e[t])?0===s:null===s||"none"===s||"0"===s||nn(s)))&&i.push(t)}i.length&&function(e,t,i){let s,r=0;for(;r<e.length&&!s;){let t=e[r];"string"==typeof t&&!nm.has(t)&&ek(t).values.length&&(s=e[r]),r++}if(s&&i)for(let r of t)e[r]=nc(i,s)}(e,i,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:i}=this;if(!e||!e.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tV[i](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let s=t[t.length-1];void 0!==s&&e.getValue(i,s).jump(s,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:i}=this;if(!e||!e.current)return;let s=e.getValue(t);s&&s.jump(this.measuredOrigin,!1);let r=i.length-1,n=i[r];i[r]=tV[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==n&&void 0===this.finalKeyframe&&(this.finalKeyframe=n),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let nf=[...nt,ex,eM],ng=e=>nf.find(ne(e)),ny={current:null},nx={current:!1},nv=new WeakMap,nb=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class nw{scrapeMotionValuesFromProps(e,t,i){return{}}constructor({parent:e,props:t,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:r,visualState:n},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tO,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=A.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,x.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=n;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=i,this.depth=e?e.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!r,this.isControllingVariants=rE(t),this.isVariantNode=rV(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:d,...u}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in u){let t=u[e];void 0!==o[e]&&L(t)&&t.set(o[e],!1)}}mount(e){this.current=e,nv.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),nx.current||function(){if(nx.current=!0,rR)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>ny.current=e.matches;e.addListener(t),t()}else ny.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ny.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),v(this.notifyUpdate),v(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let i;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let s=k.has(e);s&&this.onBindTransform&&this.onBindTransform();let r=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&x.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),n=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{r(),n(),i&&i(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in rF){let t=rF[e];if(!t)continue;let{isEnabled:i,Feature:s}=t;if(!this.features[e]&&s&&i(this.props)&&(this.features[e]=new s(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iR()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<nb.length;t++){let i=nb[t];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=e["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(e,t,i){for(let s in t){let r=t[s],n=i[s];if(L(r))e.addValue(s,r);else if(L(n))e.addValue(s,D(r,{owner:e}));else if(n!==r)if(e.hasValue(s)){let t=e.getValue(s);!0===t.liveStyle?t.jump(r):t.hasAnimated||t.set(r)}else{let t=e.getStaticValue(s);e.addValue(s,D(void 0!==t?t:r,{owner:e}))}}for(let s in i)void 0===t[s]&&e.removeValue(s);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let i=this.values.get(e);t!==i&&(i&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let i=this.values.get(e);return void 0===i&&void 0!==t&&(i=D(null===t?void 0:t,{owner:this}),this.addValue(e,i)),i}readValue(e,t){let i=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=i&&("string"==typeof i&&(ns(i)||nn(i))?i=parseFloat(i):!ng(i)&&eM.test(t)&&(i=nc(e,t)),this.setBaseTarget(e,L(i)?i.get():i)),L(i)?i.get():i}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let s=u(this.props,i,this.presenceContext?.custom);s&&(t=s[e])}if(i&&void 0!==t)return t;let s=this.getBaseTargetFromProps(this.props,e);return void 0===s||L(s)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:s}on(e,t){return this.events[e]||(this.events[e]=new M),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class nj extends nw{constructor(){super(...arguments),this.KeyframeResolver=np}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:i}){delete t[e],delete i[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;L(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function nk(e,{style:t,vars:i},s,r){for(let n in Object.assign(e.style,t,r&&r.getProjectionStyles(s)),i)e.style.setProperty(n,i[n])}class nP extends nj{constructor(){super(...arguments),this.type="html",this.renderInstance=nk}readValueFromInstance(e,t){if(k.has(t))return this.projection?.isProjecting?tT(t):tM(e,t);{let i=window.getComputedStyle(e),s=(X(t)?i.getPropertyValue(t):i[t])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(e,{transformPagePoint:t}){return iG(e,t)}build(e,t,i){rH(e,t,i.transformTemplate)}scrapeMotionValuesFromProps(e,t,i){return r4(e,t,i)}}let nT=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class nS extends nj{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iR}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(k.has(t)){let e=nh(t);return e&&e.default||0}return t=nT.has(t)?t:B(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,i){return r9(e,t,i)}build(e,t,i){rK(e,t,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(e,t,i,s){for(let i in nk(e,t,void 0,s),t.attrs)e.setAttribute(nT.has(i)?i:B(i),t.attrs[i])}mount(e){this.isSVGTag=rQ(e.tagName),super.mount(e)}}let nM=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(i,s)=>"create"===s?e:(t.has(s)||t.set(s,e(s)),t.get(s))})}((r={animation:{Feature:ip},exit:{Feature:iy},inView:{Feature:rM},tap:{Feature:rw},focus:{Feature:rc},hover:{Feature:rh},pan:{Feature:se},drag:{Feature:i9,ProjectionNode:ro,MeasureLayout:sh},layout:{ProjectionNode:ro,MeasureLayout:sh}},n=(e,t)=>r5(e)?new nS(t):new nP(t,{allowProjection:e!==o.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:i,useVisualState:s,Component:r}){function n(e,n){var l,d,u;let h,c={...(0,o.useContext)(rA),...e,layoutId:function({layoutId:e}){let t=(0,o.useContext)(sr).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:m}=c,p=function(e){let{initial:t,animate:i}=function(e,t){if(rE(e)){let{initial:t,animate:i}=e;return{initial:!1===t||ir(t)?t:void 0,animate:ir(i)?i:void 0}}return!1!==e.inherit?t:{}}(e,(0,o.useContext)(rC));return(0,o.useMemo)(()=>({initial:t,animate:i}),[rD(t),rD(i)])}(e),f=s(e,m);if(!m&&rR){d=0,u=0,(0,o.useContext)(rN).strict;let e=function(e){let{drag:t,layout:i}=rF;if(!t&&!i)return{};let s={...t,...i};return{MeasureLayout:t?.isEnabled(e)||i?.isEnabled(e)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(c);h=e.MeasureLayout,p.visualElement=function(e,t,i,s,r){let{visualElement:n}=(0,o.useContext)(rC),a=(0,o.useContext)(rN),l=(0,o.useContext)(si),d=(0,o.useContext)(rA).reducedMotion,u=(0,o.useRef)(null);s=s||a.renderer,!u.current&&s&&(u.current=s(e,{visualState:t,parent:n,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:d}));let h=u.current,c=(0,o.useContext)(sn);h&&!h.projection&&r&&("html"===h.type||"svg"===h.type)&&function(e,t,i,s){let{layoutId:r,layout:n,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:d,layoutCrossfade:u}=t;e.projection=new i(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:r,layout:n,alwaysMeasureLayout:!!a||o&&iX(o),visualElement:e,animationType:"string"==typeof n?n:"both",initialPromotionConfig:s,crossfade:u,layoutScroll:l,layoutRoot:d})}(u.current,i,r,c);let m=(0,o.useRef)(!1);(0,o.useInsertionEffect)(()=>{h&&m.current&&h.update(i,l)});let p=i[z],f=(0,o.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return rz(()=>{h&&(m.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),st.render(h.render),f.current&&h.animationState&&h.animationState.animateChanges())}),(0,o.useEffect)(()=>{h&&(!f.current&&h.animationState&&h.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),f.current=!1))}),h}(r,f,c,t,e.ProjectionNode)}return(0,a.jsxs)(rC.Provider,{value:p,children:[h&&p.visualElement?(0,a.jsx)(h,{visualElement:p.visualElement,...c}):null,i(r,e,(l=p.visualElement,(0,o.useCallback)(e=>{e&&f.onMount&&f.onMount(e),l&&(e?l.mount(e):l.unmount()),n&&("function"==typeof n?n(e):iX(n)&&(n.current=e))},[l])),f,m,p.visualElement)]})}e&&function(e){for(let t in e)rF[t]={...rF[t],...e[t]}}(e),n.displayName=`motion.${"string"==typeof r?r:`create(${r.displayName??r.name??""})`}`;let l=(0,o.forwardRef)(n);return l[rB]=r,l}({...r5(e)?r7:r8,preloadedFeatures:r,useRender:function(e=!1){return(t,i,s,{latestValues:r},n)=>{let a=(r5(t)?function(e,t,i,s){let r=(0,o.useMemo)(()=>{let i=rZ();return rK(i,t,rQ(s),e.transformTemplate,e.style),{...i.attrs,style:{...i.style}}},[t]);if(e.style){let t={};rY(t,e.style,e),r.style={...t,...r.style}}return r}:function(e,t){let i={},s=function(e,t){let i=e.style||{},s={};return rY(s,i,e),Object.assign(s,function({transformTemplate:e},t){return(0,o.useMemo)(()=>{let i=rG();return rH(i,t,e),Object.assign({},i.vars,i.style)},[t])}(e,t)),s}(e,t);return e.drag&&!1!==e.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,r,n,t),l=function(e,t,i){let s={};for(let r in e)("values"!==r||"object"!=typeof e.values)&&(r1(r)||!0===i&&r0(r)||!t&&!r0(r)||e.draggable&&r.startsWith("onDrag"))&&(s[r]=e[r]);return s}(i,"string"==typeof t,e),d=t!==o.Fragment?{...l,...a,ref:s}:{},{children:u}=i,h=(0,o.useMemo)(()=>L(u)?u.get():u,[u]);return(0,o.createElement)(t,{...d,children:h})}}(t),createVisualElement:n,Component:e})})),nN=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),nA=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,i)=>i?i.toUpperCase():t.toLowerCase()),nC=e=>{let t=nA(e);return t.charAt(0).toUpperCase()+t.slice(1)},nE=(...e)=>e.filter((e,t,i)=>!!e&&""!==e.trim()&&i.indexOf(e)===t).join(" ").trim(),nV=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var nD={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let nR=(0,o.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:i=2,absoluteStrokeWidth:s,className:r="",children:n,iconNode:a,...l},d)=>(0,o.createElement)("svg",{ref:d,...nD,width:t,height:t,stroke:e,strokeWidth:s?24*Number(i)/Number(t):i,className:nE("lucide",r),...!n&&!nV(l)&&{"aria-hidden":"true"},...l},[...a.map(([e,t])=>(0,o.createElement)(e,t)),...Array.isArray(n)?n:[n]])),nL=(e,t)=>{let i=(0,o.forwardRef)(({className:i,...s},r)=>(0,o.createElement)(nR,{ref:r,iconNode:t,className:nE(`lucide-${nN(nC(e))}`,`lucide-${e}`,i),...s}));return i.displayName=nC(e),i},nF=nL("hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]),nB=nL("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]),nz=nL("shapes",[["path",{d:"M8.3 10a.7.7 0 0 1-.626-1.079L11.4 3a.7.7 0 0 1 1.198-.043L16.3 8.9a.7.7 0 0 1-.572 1.1Z",key:"1bo67w"}],["rect",{x:"3",y:"14",width:"7",height:"7",rx:"1",key:"1bkyp8"}],["circle",{cx:"17.5",cy:"17.5",r:"3.5",key:"w3z12y"}]]),nO=nL("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),nI=nL("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),n$=nL("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),nU=nL("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]),nW=nL("trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]),nq=e=>{let t=nX(e),{conflictingClassGroups:i,conflictingClassGroupModifiers:s}=e;return{getClassGroupId:e=>{let i=e.split("-");return""===i[0]&&1!==i.length&&i.shift(),nH(i,t)||nY(e)},getConflictingClassGroupIds:(e,t)=>{let r=i[e]||[];return t&&s[e]?[...r,...s[e]]:r}}},nH=(e,t)=>{if(0===e.length)return t.classGroupId;let i=e[0],s=t.nextPart.get(i),r=s?nH(e.slice(1),s):void 0;if(r)return r;if(0===t.validators.length)return;let n=e.join("-");return t.validators.find(({validator:e})=>e(n))?.classGroupId},nG=/^\[(.+)\]$/,nY=e=>{if(nG.test(e)){let t=nG.exec(e)[1],i=t?.substring(0,t.indexOf(":"));if(i)return"arbitrary.."+i}},nX=e=>{let{theme:t,classGroups:i}=e,s={nextPart:new Map,validators:[]};for(let e in i)n_(i[e],s,e,t);return s},n_=(e,t,i,s)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:nK(t,e)).classGroupId=i;return}if("function"==typeof e)return nZ(e)?void n_(e(s),t,i,s):void t.validators.push({validator:e,classGroupId:i});Object.entries(e).forEach(([e,r])=>{n_(r,nK(t,e),i,s)})})},nK=(e,t)=>{let i=e;return t.split("-").forEach(e=>{i.nextPart.has(e)||i.nextPart.set(e,{nextPart:new Map,validators:[]}),i=i.nextPart.get(e)}),i},nZ=e=>e.isThemeGetter,nQ=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,i=new Map,s=new Map,r=(r,n)=>{i.set(r,n),++t>e&&(t=0,s=i,i=new Map)};return{get(e){let t=i.get(e);return void 0!==t?t:void 0!==(t=s.get(e))?(r(e,t),t):void 0},set(e,t){i.has(e)?i.set(e,t):r(e,t)}}},nJ=e=>{let{prefix:t,experimentalParseClassName:i}=e,s=e=>{let t,i=[],s=0,r=0,n=0;for(let a=0;a<e.length;a++){let o=e[a];if(0===s&&0===r){if(":"===o){i.push(e.slice(n,a)),n=a+1;continue}if("/"===o){t=a;continue}}"["===o?s++:"]"===o?s--:"("===o?r++:")"===o&&r--}let a=0===i.length?e:e.substring(n),o=n0(a);return{modifiers:i,hasImportantModifier:o!==a,baseClassName:o,maybePostfixModifierPosition:t&&t>n?t-n:void 0}};if(t){let e=t+":",i=s;s=t=>t.startsWith(e)?i(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(i){let e=s;s=t=>i({className:t,parseClassName:e})}return s},n0=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,n1=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let i=[],s=[];return e.forEach(e=>{"["===e[0]||t[e]?(i.push(...s.sort(),e),s=[]):s.push(e)}),i.push(...s.sort()),i}},n2=e=>({cache:nQ(e.cacheSize),parseClassName:nJ(e),sortModifiers:n1(e),...nq(e)}),n5=/\s+/,n6=(e,t)=>{let{parseClassName:i,getClassGroupId:s,getConflictingClassGroupIds:r,sortModifiers:n}=t,a=[],o=e.trim().split(n5),l="";for(let e=o.length-1;e>=0;e-=1){let t=o[e],{isExternal:d,modifiers:u,hasImportantModifier:h,baseClassName:c,maybePostfixModifierPosition:m}=i(t);if(d){l=t+(l.length>0?" "+l:l);continue}let p=!!m,f=s(p?c.substring(0,m):c);if(!f){if(!p||!(f=s(c))){l=t+(l.length>0?" "+l:l);continue}p=!1}let g=n(u).join(":"),y=h?g+"!":g,x=y+f;if(a.includes(x))continue;a.push(x);let v=r(f,p);for(let e=0;e<v.length;++e){let t=v[e];a.push(y+t)}l=t+(l.length>0?" "+l:l)}return l};function n3(){let e,t,i=0,s="";for(;i<arguments.length;)(e=arguments[i++])&&(t=n4(e))&&(s&&(s+=" "),s+=t);return s}let n4=e=>{let t;if("string"==typeof e)return e;let i="";for(let s=0;s<e.length;s++)e[s]&&(t=n4(e[s]))&&(i&&(i+=" "),i+=t);return i},n8=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},n9=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,n7=/^\((?:(\w[\w-]*):)?(.+)\)$/i,ae=/^\d+\/\d+$/,at=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,ai=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,as=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,ar=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,an=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,aa=e=>ae.test(e),ao=e=>!!e&&!Number.isNaN(Number(e)),al=e=>!!e&&Number.isInteger(Number(e)),ad=e=>e.endsWith("%")&&ao(e.slice(0,-1)),au=e=>at.test(e),ah=()=>!0,ac=e=>ai.test(e)&&!as.test(e),am=()=>!1,ap=e=>ar.test(e),af=e=>an.test(e),ag=e=>!ax(e)&&!aP(e),ay=e=>aE(e,aL,am),ax=e=>n9.test(e),av=e=>aE(e,aF,ac),ab=e=>aE(e,aB,ao),aw=e=>aE(e,aD,am),aj=e=>aE(e,aR,af),ak=e=>aE(e,aO,ap),aP=e=>n7.test(e),aT=e=>aV(e,aF),aS=e=>aV(e,az),aM=e=>aV(e,aD),aN=e=>aV(e,aL),aA=e=>aV(e,aR),aC=e=>aV(e,aO,!0),aE=(e,t,i)=>{let s=n9.exec(e);return!!s&&(s[1]?t(s[1]):i(s[2]))},aV=(e,t,i=!1)=>{let s=n7.exec(e);return!!s&&(s[1]?t(s[1]):i)},aD=e=>"position"===e||"percentage"===e,aR=e=>"image"===e||"url"===e,aL=e=>"length"===e||"size"===e||"bg-size"===e,aF=e=>"length"===e,aB=e=>"number"===e,az=e=>"family-name"===e,aO=e=>"shadow"===e;Symbol.toStringTag;let aI=function(e,...t){let i,s,r,n=function(o){return s=(i=n2(t.reduce((e,t)=>t(e),e()))).cache.get,r=i.cache.set,n=a,a(o)};function a(e){let t=s(e);if(t)return t;let n=n6(e,i);return r(e,n),n}return function(){return n(n3.apply(null,arguments))}}(()=>{let e=n8("color"),t=n8("font"),i=n8("text"),s=n8("font-weight"),r=n8("tracking"),n=n8("leading"),a=n8("breakpoint"),o=n8("container"),l=n8("spacing"),d=n8("radius"),u=n8("shadow"),h=n8("inset-shadow"),c=n8("text-shadow"),m=n8("drop-shadow"),p=n8("blur"),f=n8("perspective"),g=n8("aspect"),y=n8("ease"),x=n8("animate"),v=()=>["auto","avoid","all","avoid-page","page","left","right","column"],b=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...b(),aP,ax],j=()=>["auto","hidden","clip","visible","scroll"],k=()=>["auto","contain","none"],P=()=>[aP,ax,l],T=()=>[aa,"full","auto",...P()],S=()=>[al,"none","subgrid",aP,ax],M=()=>["auto",{span:["full",al,aP,ax]},al,aP,ax],N=()=>[al,"auto",aP,ax],A=()=>["auto","min","max","fr",aP,ax],C=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],E=()=>["start","end","center","stretch","center-safe","end-safe"],V=()=>["auto",...P()],D=()=>[aa,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...P()],R=()=>[e,aP,ax],L=()=>[...b(),aM,aw,{position:[aP,ax]}],F=()=>["no-repeat",{repeat:["","x","y","space","round"]}],B=()=>["auto","cover","contain",aN,ay,{size:[aP,ax]}],z=()=>[ad,aT,av],O=()=>["","none","full",d,aP,ax],I=()=>["",ao,aT,av],$=()=>["solid","dashed","dotted","double"],U=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],W=()=>[ao,ad,aM,aw],q=()=>["","none",p,aP,ax],H=()=>["none",ao,aP,ax],G=()=>["none",ao,aP,ax],Y=()=>[ao,aP,ax],X=()=>[aa,"full",...P()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[au],breakpoint:[au],color:[ah],container:[au],"drop-shadow":[au],ease:["in","out","in-out"],font:[ag],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[au],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[au],shadow:[au],spacing:["px",ao],text:[au],"text-shadow":[au],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",aa,ax,aP,g]}],container:["container"],columns:[{columns:[ao,ax,aP,o]}],"break-after":[{"break-after":v()}],"break-before":[{"break-before":v()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:j()}],"overflow-x":[{"overflow-x":j()}],"overflow-y":[{"overflow-y":j()}],overscroll:[{overscroll:k()}],"overscroll-x":[{"overscroll-x":k()}],"overscroll-y":[{"overscroll-y":k()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:T()}],"inset-x":[{"inset-x":T()}],"inset-y":[{"inset-y":T()}],start:[{start:T()}],end:[{end:T()}],top:[{top:T()}],right:[{right:T()}],bottom:[{bottom:T()}],left:[{left:T()}],visibility:["visible","invisible","collapse"],z:[{z:[al,"auto",aP,ax]}],basis:[{basis:[aa,"full","auto",o,...P()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[ao,aa,"auto","initial","none",ax]}],grow:[{grow:["",ao,aP,ax]}],shrink:[{shrink:["",ao,aP,ax]}],order:[{order:[al,"first","last","none",aP,ax]}],"grid-cols":[{"grid-cols":S()}],"col-start-end":[{col:M()}],"col-start":[{"col-start":N()}],"col-end":[{"col-end":N()}],"grid-rows":[{"grid-rows":S()}],"row-start-end":[{row:M()}],"row-start":[{"row-start":N()}],"row-end":[{"row-end":N()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":A()}],"auto-rows":[{"auto-rows":A()}],gap:[{gap:P()}],"gap-x":[{"gap-x":P()}],"gap-y":[{"gap-y":P()}],"justify-content":[{justify:[...C(),"normal"]}],"justify-items":[{"justify-items":[...E(),"normal"]}],"justify-self":[{"justify-self":["auto",...E()]}],"align-content":[{content:["normal",...C()]}],"align-items":[{items:[...E(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...E(),{baseline:["","last"]}]}],"place-content":[{"place-content":C()}],"place-items":[{"place-items":[...E(),"baseline"]}],"place-self":[{"place-self":["auto",...E()]}],p:[{p:P()}],px:[{px:P()}],py:[{py:P()}],ps:[{ps:P()}],pe:[{pe:P()}],pt:[{pt:P()}],pr:[{pr:P()}],pb:[{pb:P()}],pl:[{pl:P()}],m:[{m:V()}],mx:[{mx:V()}],my:[{my:V()}],ms:[{ms:V()}],me:[{me:V()}],mt:[{mt:V()}],mr:[{mr:V()}],mb:[{mb:V()}],ml:[{ml:V()}],"space-x":[{"space-x":P()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":P()}],"space-y-reverse":["space-y-reverse"],size:[{size:D()}],w:[{w:[o,"screen",...D()]}],"min-w":[{"min-w":[o,"screen","none",...D()]}],"max-w":[{"max-w":[o,"screen","none","prose",{screen:[a]},...D()]}],h:[{h:["screen","lh",...D()]}],"min-h":[{"min-h":["screen","lh","none",...D()]}],"max-h":[{"max-h":["screen","lh",...D()]}],"font-size":[{text:["base",i,aT,av]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[s,aP,ab]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",ad,ax]}],"font-family":[{font:[aS,ax,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[r,aP,ax]}],"line-clamp":[{"line-clamp":[ao,"none",aP,ab]}],leading:[{leading:[n,...P()]}],"list-image":[{"list-image":["none",aP,ax]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",aP,ax]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:R()}],"text-color":[{text:R()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...$(),"wavy"]}],"text-decoration-thickness":[{decoration:[ao,"from-font","auto",aP,av]}],"text-decoration-color":[{decoration:R()}],"underline-offset":[{"underline-offset":[ao,"auto",aP,ax]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:P()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",aP,ax]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",aP,ax]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:L()}],"bg-repeat":[{bg:F()}],"bg-size":[{bg:B()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},al,aP,ax],radial:["",aP,ax],conic:[al,aP,ax]},aA,aj]}],"bg-color":[{bg:R()}],"gradient-from-pos":[{from:z()}],"gradient-via-pos":[{via:z()}],"gradient-to-pos":[{to:z()}],"gradient-from":[{from:R()}],"gradient-via":[{via:R()}],"gradient-to":[{to:R()}],rounded:[{rounded:O()}],"rounded-s":[{"rounded-s":O()}],"rounded-e":[{"rounded-e":O()}],"rounded-t":[{"rounded-t":O()}],"rounded-r":[{"rounded-r":O()}],"rounded-b":[{"rounded-b":O()}],"rounded-l":[{"rounded-l":O()}],"rounded-ss":[{"rounded-ss":O()}],"rounded-se":[{"rounded-se":O()}],"rounded-ee":[{"rounded-ee":O()}],"rounded-es":[{"rounded-es":O()}],"rounded-tl":[{"rounded-tl":O()}],"rounded-tr":[{"rounded-tr":O()}],"rounded-br":[{"rounded-br":O()}],"rounded-bl":[{"rounded-bl":O()}],"border-w":[{border:I()}],"border-w-x":[{"border-x":I()}],"border-w-y":[{"border-y":I()}],"border-w-s":[{"border-s":I()}],"border-w-e":[{"border-e":I()}],"border-w-t":[{"border-t":I()}],"border-w-r":[{"border-r":I()}],"border-w-b":[{"border-b":I()}],"border-w-l":[{"border-l":I()}],"divide-x":[{"divide-x":I()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":I()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...$(),"hidden","none"]}],"divide-style":[{divide:[...$(),"hidden","none"]}],"border-color":[{border:R()}],"border-color-x":[{"border-x":R()}],"border-color-y":[{"border-y":R()}],"border-color-s":[{"border-s":R()}],"border-color-e":[{"border-e":R()}],"border-color-t":[{"border-t":R()}],"border-color-r":[{"border-r":R()}],"border-color-b":[{"border-b":R()}],"border-color-l":[{"border-l":R()}],"divide-color":[{divide:R()}],"outline-style":[{outline:[...$(),"none","hidden"]}],"outline-offset":[{"outline-offset":[ao,aP,ax]}],"outline-w":[{outline:["",ao,aT,av]}],"outline-color":[{outline:R()}],shadow:[{shadow:["","none",u,aC,ak]}],"shadow-color":[{shadow:R()}],"inset-shadow":[{"inset-shadow":["none",h,aC,ak]}],"inset-shadow-color":[{"inset-shadow":R()}],"ring-w":[{ring:I()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:R()}],"ring-offset-w":[{"ring-offset":[ao,av]}],"ring-offset-color":[{"ring-offset":R()}],"inset-ring-w":[{"inset-ring":I()}],"inset-ring-color":[{"inset-ring":R()}],"text-shadow":[{"text-shadow":["none",c,aC,ak]}],"text-shadow-color":[{"text-shadow":R()}],opacity:[{opacity:[ao,aP,ax]}],"mix-blend":[{"mix-blend":[...U(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":U()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[ao]}],"mask-image-linear-from-pos":[{"mask-linear-from":W()}],"mask-image-linear-to-pos":[{"mask-linear-to":W()}],"mask-image-linear-from-color":[{"mask-linear-from":R()}],"mask-image-linear-to-color":[{"mask-linear-to":R()}],"mask-image-t-from-pos":[{"mask-t-from":W()}],"mask-image-t-to-pos":[{"mask-t-to":W()}],"mask-image-t-from-color":[{"mask-t-from":R()}],"mask-image-t-to-color":[{"mask-t-to":R()}],"mask-image-r-from-pos":[{"mask-r-from":W()}],"mask-image-r-to-pos":[{"mask-r-to":W()}],"mask-image-r-from-color":[{"mask-r-from":R()}],"mask-image-r-to-color":[{"mask-r-to":R()}],"mask-image-b-from-pos":[{"mask-b-from":W()}],"mask-image-b-to-pos":[{"mask-b-to":W()}],"mask-image-b-from-color":[{"mask-b-from":R()}],"mask-image-b-to-color":[{"mask-b-to":R()}],"mask-image-l-from-pos":[{"mask-l-from":W()}],"mask-image-l-to-pos":[{"mask-l-to":W()}],"mask-image-l-from-color":[{"mask-l-from":R()}],"mask-image-l-to-color":[{"mask-l-to":R()}],"mask-image-x-from-pos":[{"mask-x-from":W()}],"mask-image-x-to-pos":[{"mask-x-to":W()}],"mask-image-x-from-color":[{"mask-x-from":R()}],"mask-image-x-to-color":[{"mask-x-to":R()}],"mask-image-y-from-pos":[{"mask-y-from":W()}],"mask-image-y-to-pos":[{"mask-y-to":W()}],"mask-image-y-from-color":[{"mask-y-from":R()}],"mask-image-y-to-color":[{"mask-y-to":R()}],"mask-image-radial":[{"mask-radial":[aP,ax]}],"mask-image-radial-from-pos":[{"mask-radial-from":W()}],"mask-image-radial-to-pos":[{"mask-radial-to":W()}],"mask-image-radial-from-color":[{"mask-radial-from":R()}],"mask-image-radial-to-color":[{"mask-radial-to":R()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":b()}],"mask-image-conic-pos":[{"mask-conic":[ao]}],"mask-image-conic-from-pos":[{"mask-conic-from":W()}],"mask-image-conic-to-pos":[{"mask-conic-to":W()}],"mask-image-conic-from-color":[{"mask-conic-from":R()}],"mask-image-conic-to-color":[{"mask-conic-to":R()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:L()}],"mask-repeat":[{mask:F()}],"mask-size":[{mask:B()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",aP,ax]}],filter:[{filter:["","none",aP,ax]}],blur:[{blur:q()}],brightness:[{brightness:[ao,aP,ax]}],contrast:[{contrast:[ao,aP,ax]}],"drop-shadow":[{"drop-shadow":["","none",m,aC,ak]}],"drop-shadow-color":[{"drop-shadow":R()}],grayscale:[{grayscale:["",ao,aP,ax]}],"hue-rotate":[{"hue-rotate":[ao,aP,ax]}],invert:[{invert:["",ao,aP,ax]}],saturate:[{saturate:[ao,aP,ax]}],sepia:[{sepia:["",ao,aP,ax]}],"backdrop-filter":[{"backdrop-filter":["","none",aP,ax]}],"backdrop-blur":[{"backdrop-blur":q()}],"backdrop-brightness":[{"backdrop-brightness":[ao,aP,ax]}],"backdrop-contrast":[{"backdrop-contrast":[ao,aP,ax]}],"backdrop-grayscale":[{"backdrop-grayscale":["",ao,aP,ax]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[ao,aP,ax]}],"backdrop-invert":[{"backdrop-invert":["",ao,aP,ax]}],"backdrop-opacity":[{"backdrop-opacity":[ao,aP,ax]}],"backdrop-saturate":[{"backdrop-saturate":[ao,aP,ax]}],"backdrop-sepia":[{"backdrop-sepia":["",ao,aP,ax]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":P()}],"border-spacing-x":[{"border-spacing-x":P()}],"border-spacing-y":[{"border-spacing-y":P()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",aP,ax]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[ao,"initial",aP,ax]}],ease:[{ease:["linear","initial",y,aP,ax]}],delay:[{delay:[ao,aP,ax]}],animate:[{animate:["none",x,aP,ax]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,aP,ax]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:H()}],"rotate-x":[{"rotate-x":H()}],"rotate-y":[{"rotate-y":H()}],"rotate-z":[{"rotate-z":H()}],scale:[{scale:G()}],"scale-x":[{"scale-x":G()}],"scale-y":[{"scale-y":G()}],"scale-z":[{"scale-z":G()}],"scale-3d":["scale-3d"],skew:[{skew:Y()}],"skew-x":[{"skew-x":Y()}],"skew-y":[{"skew-y":Y()}],transform:[{transform:[aP,ax,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:X()}],"translate-x":[{"translate-x":X()}],"translate-y":[{"translate-y":X()}],"translate-z":[{"translate-z":X()}],"translate-none":["translate-none"],accent:[{accent:R()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:R()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",aP,ax]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":P()}],"scroll-mx":[{"scroll-mx":P()}],"scroll-my":[{"scroll-my":P()}],"scroll-ms":[{"scroll-ms":P()}],"scroll-me":[{"scroll-me":P()}],"scroll-mt":[{"scroll-mt":P()}],"scroll-mr":[{"scroll-mr":P()}],"scroll-mb":[{"scroll-mb":P()}],"scroll-ml":[{"scroll-ml":P()}],"scroll-p":[{"scroll-p":P()}],"scroll-px":[{"scroll-px":P()}],"scroll-py":[{"scroll-py":P()}],"scroll-ps":[{"scroll-ps":P()}],"scroll-pe":[{"scroll-pe":P()}],"scroll-pt":[{"scroll-pt":P()}],"scroll-pr":[{"scroll-pr":P()}],"scroll-pb":[{"scroll-pb":P()}],"scroll-pl":[{"scroll-pl":P()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",aP,ax]}],fill:[{fill:["none",...R()]}],"stroke-w":[{stroke:[ao,aT,av,ab]}],stroke:[{stroke:["none",...R()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function a$(...e){return aI(function(){for(var e,t,i=0,s="",r=arguments.length;i<r;i++)(e=arguments[i])&&(t=function e(t){var i,s,r="";if("string"==typeof t||"number"==typeof t)r+=t;else if("object"==typeof t)if(Array.isArray(t)){var n=t.length;for(i=0;i<n;i++)t[i]&&(s=e(t[i]))&&(r&&(r+=" "),r+=s)}else for(s in t)t[s]&&(r&&(r+=" "),r+=s);return r}(e))&&(s&&(s+=" "),s+=t);return s}(e))}let aU=(0,o.forwardRef)(({className:e,variant:t="default",size:i="default",...s},r)=>(0,a.jsx)("button",{className:a$("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-primary text-primary-foreground hover:bg-primary/90":"default"===t,"bg-destructive text-destructive-foreground hover:bg-destructive/90":"destructive"===t,"border border-input bg-background hover:bg-accent hover:text-accent-foreground":"outline"===t,"bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===t,"hover:bg-accent hover:text-accent-foreground":"ghost"===t,"text-primary underline-offset-4 hover:underline":"link"===t},{"h-10 px-4 py-2":"default"===i,"h-9 rounded-md px-3":"sm"===i,"h-11 rounded-md px-8":"lg"===i,"h-10 w-10":"icon"===i},e),ref:r,...s}));aU.displayName="Button";let aW=(0,o.forwardRef)(({className:e,...t},i)=>(0,a.jsx)("div",{ref:i,className:a$("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));aW.displayName="Card",(0,o.forwardRef)(({className:e,...t},i)=>(0,a.jsx)("div",{ref:i,className:a$("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",(0,o.forwardRef)(({className:e,...t},i)=>(0,a.jsx)("h3",{ref:i,className:a$("text-2xl font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",(0,o.forwardRef)(({className:e,...t},i)=>(0,a.jsx)("p",{ref:i,className:a$("text-sm text-muted-foreground",e),...t})).displayName="CardDescription",(0,o.forwardRef)(({className:e,...t},i)=>(0,a.jsx)("div",{ref:i,className:a$("p-6 pt-0",e),...t})).displayName="CardContent",(0,o.forwardRef)(({className:e,...t},i)=>(0,a.jsx)("div",{ref:i,className:a$("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter";class aq extends o.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,i=tJ(e)&&e.offsetWidth||0,s=this.props.sizeRef.current;s.height=t.offsetHeight||0,s.width=t.offsetWidth||0,s.top=t.offsetTop,s.left=t.offsetLeft,s.right=i-s.width-s.left}return null}componentDidUpdate(){}render(){return this.props.children}}function aH({children:e,isPresent:t,anchorX:i}){let s=(0,o.useId)(),r=(0,o.useRef)(null),n=(0,o.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,o.useContext)(rA);return(0,o.useInsertionEffect)(()=>{let{width:e,height:a,top:o,left:d,right:u}=n.current;if(t||!r.current||!e||!a)return;let h="left"===i?`left: ${d}`:`right: ${u}`;r.current.dataset.motionPopId=s;let c=document.createElement("style");return l&&(c.nonce=l),document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${s}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${a}px !important;
            ${h}px !important;
            top: ${o}px !important;
          }
        `),()=>{document.head.contains(c)&&document.head.removeChild(c)}},[t]),(0,a.jsx)(aq,{isPresent:t,childRef:r,sizeRef:n,children:o.cloneElement(e,{ref:r})})}let aG=({children:e,initial:t,isPresent:i,onExitComplete:s,custom:r,presenceAffectsLayout:n,mode:l,anchorX:d})=>{let u=r6(aY),h=(0,o.useId)(),c=!0,m=(0,o.useMemo)(()=>(c=!1,{id:h,initial:t,isPresent:i,custom:r,onExitComplete:e=>{for(let t of(u.set(e,!0),u.values()))if(!t)return;s&&s()},register:e=>(u.set(e,!1),()=>u.delete(e))}),[i,u,s]);return n&&c&&(m={...m}),(0,o.useMemo)(()=>{u.forEach((e,t)=>u.set(t,!1))},[i]),o.useEffect(()=>{i||u.size||!s||s()},[i]),"popLayout"===l&&(e=(0,a.jsx)(aH,{isPresent:i,anchorX:d,children:e})),(0,a.jsx)(si.Provider,{value:m,children:e})};function aY(){return new Map}let aX=e=>e.key||"";function a_(e){let t=[];return o.Children.forEach(e,e=>{(0,o.isValidElement)(e)&&t.push(e)}),t}let aK=({children:e,custom:t,initial:i=!0,onExitComplete:s,presenceAffectsLayout:r=!0,mode:n="sync",propagate:l=!1,anchorX:d="left"})=>{let[u,h]=ss(l),c=(0,o.useMemo)(()=>a_(e),[e]),m=l&&!u?[]:c.map(aX),p=(0,o.useRef)(!0),f=(0,o.useRef)(c),g=r6(()=>new Map),[y,x]=(0,o.useState)(c),[v,b]=(0,o.useState)(c);rz(()=>{p.current=!1,f.current=c;for(let e=0;e<v.length;e++){let t=aX(v[e]);m.includes(t)?g.delete(t):!0!==g.get(t)&&g.set(t,!1)}},[v,m.length,m.join("-")]);let w=[];if(c!==y){let e=[...c];for(let t=0;t<v.length;t++){let i=v[t],s=aX(i);m.includes(s)||(e.splice(t,0,i),w.push(i))}return"wait"===n&&w.length&&(e=w),b(a_(e)),x(c),null}let{forceRender:j}=(0,o.useContext)(sr);return(0,a.jsx)(a.Fragment,{children:v.map(e=>{let o=aX(e),y=(!l||!!u)&&(c===v||m.includes(o));return(0,a.jsx)(aG,{isPresent:y,initial:(!p.current||!!i)&&void 0,custom:t,presenceAffectsLayout:r,mode:n,onExitComplete:y?void 0:()=>{if(!g.has(o))return;g.set(o,!0);let e=!0;g.forEach(t=>{t||(e=!1)}),e&&(j?.(),b(f.current),l&&h?.(),s&&s())},anchorX:d,children:e},o)})})},aZ=nL("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),aQ=nL("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),aJ=[{id:"circle",name:"Circle",svg:'<circle cx="50" cy="50" r="40" />',color:"#FF6B6B"},{id:"square",name:"Square",svg:'<rect x="10" y="10" width="80" height="80" />',color:"#4ECDC4"},{id:"triangle",name:"Triangle",svg:'<polygon points="50,10 90,90 10,90" />',color:"#45B7D1"},{id:"rectangle",name:"Rectangle",svg:'<rect x="10" y="25" width="80" height="50" />',color:"#96CEB4"},{id:"star",name:"Star",svg:'<polygon points="50,5 61,35 95,35 68,57 79,91 50,70 21,91 32,57 5,35 39,35" />',color:"#FFEAA7"},{id:"heart",name:"Heart",svg:'<path d="M50,85 C50,85 20,60 20,40 C20,25 30,15 45,15 C47,15 50,20 50,20 C50,20 53,15 55,15 C70,15 80,25 80,40 C80,60 50,85 50,85 Z" />',color:"#FD79A8"}],a0=[{id:"counting",title:"Counting Game",description:"Count objects and learn numbers 1-10",icon:(0,a.jsx)(nF,{className:"h-8 w-8"}),grade:"Grade 1",difficulty:"Easy",color:"from-blue-400 to-blue-600",component:function({maxNumber:e=10,onComplete:t}){let[i,s]=(0,o.useState)(1),[r,n]=(0,o.useState)(0),[l,d]=(0,o.useState)(null),[u,h]=(0,o.useState)(null),[c,m]=(0,o.useState)(!1),[p,f]=(0,o.useState)(!1),g=e=>{let t=["\uD83C\uDF4E","\uD83C\uDF1F","\uD83C\uDF88","\uD83D\uDC31","\uD83D\uDE97","\uD83C\uDF38","\uD83C\uDF81","\uD83E\uDD8B"],i=t[Math.floor(Math.random()*t.length)];return Array(e).fill(i)},[y,x]=(0,o.useState)(g(i)),v=t=>{let i=[t];for(;i.length<4;){let t=Math.floor(Math.random()*e)+1;i.includes(t)||i.push(t)}return i.sort(()=>Math.random()-.5)},[b,w]=(0,o.useState)(v(i)),j=s=>{d(s);let a=s===i;h(a),a&&(n(r+10),f(!0),setTimeout(()=>f(!1),1e3)),setTimeout(()=>{i>=e?(m(!0),t?.(r+10*!!a)):k()},1500)},k=()=>{let e=i+1;s(e),x(g(e)),w(v(e)),d(null),h(null)};return c?(0,a.jsxs)(aW,{className:"p-8 text-center max-w-md mx-auto",children:[(0,a.jsxs)(nM.div,{initial:{scale:0},animate:{scale:1},className:"mb-6",children:[(0,a.jsx)(nW,{className:"h-16 w-16 text-yellow-500 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Congratulations!"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"You completed the counting game!"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-100 to-blue-100 p-4 rounded-lg mb-6",children:(0,a.jsxs)("p",{className:"text-lg font-semibold text-purple-700",children:["Final Score: ",r," points"]})})]}),(0,a.jsxs)(aU,{onClick:()=>{s(1),n(0),d(null),h(null),m(!1),x(g(1)),w(v(1))},className:"w-full",children:[(0,a.jsx)(aZ,{className:"mr-2 h-4 w-4"}),"Play Again"]})]}):(0,a.jsxs)(aW,{className:"p-6 max-w-2xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n$,{className:"h-6 w-6 text-yellow-500"}),(0,a.jsxs)("span",{className:"text-lg font-semibold",children:["Score: ",r]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Question ",i," of ",e]})]}),(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Count the objects below:"}),(0,a.jsx)("div",{className:"grid grid-cols-5 gap-4 justify-items-center mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg",children:(0,a.jsx)(aK,{children:y.map((e,t)=>(0,a.jsx)(nM.div,{initial:{scale:0,rotate:-180},animate:{scale:1,rotate:0},transition:{delay:.1*t},className:"text-4xl",children:e},t))})})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:b.map(e=>(0,a.jsx)(nM.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,a.jsx)(aU,{onClick:()=>j(e),disabled:null!==l,variant:l===e?u?"default":"destructive":"outline",className:"w-full h-16 text-2xl font-bold",children:e})},e))}),(0,a.jsx)(aK,{children:null!==u&&(0,a.jsx)(nM.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:`text-center p-4 rounded-lg ${u?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:u?"\uD83C\uDF89 Correct! Well done!":"❌ Try again next time!"})}),(0,a.jsx)(aK,{children:p&&(0,a.jsx)(nM.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 pointer-events-none flex items-center justify-center z-50",children:(0,a.jsx)(nM.div,{animate:{scale:[1,1.2,1],rotate:[0,360,0]},transition:{duration:1},className:"text-6xl",children:"⭐"})})})]})}},{id:"addition",title:"Addition Challenge",description:"Practice addition with fun problems",icon:(0,a.jsx)(nB,{className:"h-8 w-8"}),grade:"Grade 1-2",difficulty:"Easy",color:"from-green-400 to-green-600",component:function({maxNumber:e=10,onComplete:t}){let[i,s]=(0,o.useState)(1),[r,n]=(0,o.useState)(0),[l,d]=(0,o.useState)(null),[u,h]=(0,o.useState)(null),[c,m]=(0,o.useState)(!1),[p,f]=(0,o.useState)(!1),g=()=>{let t=Math.floor(Math.random()*e)+1,i=Math.floor(Math.random()*e)+1;return{num1:t,num2:i,answer:t+i}},[y,x]=(0,o.useState)(g()),v=e=>{let t=[e];for(;t.length<4;){let i=e+Math.floor(6*Math.random())-3;i>0&&!t.includes(i)&&t.push(i)}return t.sort(()=>Math.random()-.5)},[b,w]=(0,o.useState)(v(y.answer)),j=e=>{d(e);let s=e===y.answer;h(s),s&&(n(r+10),f(!0),setTimeout(()=>f(!1),1e3)),setTimeout(()=>{i>=10?(m(!0),t?.(r+10*!!s)):k()},1500)},k=()=>{let e=g();s(i+1),x(e),w(v(e.answer)),d(null),h(null)};return c?(0,a.jsxs)(aW,{className:"p-8 text-center max-w-md mx-auto",children:[(0,a.jsxs)(nM.div,{initial:{scale:0},animate:{scale:1},className:"mb-6",children:[(0,a.jsx)(nW,{className:"h-16 w-16 text-yellow-500 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Amazing Work!"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"You completed the addition challenge!"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-green-100 to-blue-100 p-4 rounded-lg mb-6",children:(0,a.jsxs)("p",{className:"text-lg font-semibold text-green-700",children:["Final Score: ",r," points"]})})]}),(0,a.jsxs)(aU,{onClick:()=>{s(1),n(0),d(null),h(null),m(!1);let e=g();x(e),w(v(e.answer))},className:"w-full",children:[(0,a.jsx)(aZ,{className:"mr-2 h-4 w-4"}),"Play Again"]})]}):(0,a.jsxs)(aW,{className:"p-6 max-w-2xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n$,{className:"h-6 w-6 text-yellow-500"}),(0,a.jsxs)("span",{className:"text-lg font-semibold",children:["Score: ",r]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Problem ",i," of 10"]})]}),(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Solve the Addition Problem:"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-50 to-blue-50 p-8 rounded-lg mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4 text-6xl font-bold text-purple-700",children:[(0,a.jsx)(nM.span,{initial:{scale:0},animate:{scale:1},transition:{delay:.2},children:y.num1}),(0,a.jsx)(nM.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.4},children:(0,a.jsx)(aQ,{className:"h-12 w-12"})}),(0,a.jsx)(nM.span,{initial:{scale:0},animate:{scale:1},transition:{delay:.6},children:y.num2}),(0,a.jsx)(nM.span,{initial:{scale:0},animate:{scale:1},transition:{delay:.8},children:"="}),(0,a.jsx)(nM.span,{initial:{scale:0},animate:{scale:1},transition:{delay:1},className:"text-gray-400",children:"?"})]})})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:b.map(e=>(0,a.jsx)(nM.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,a.jsx)(aU,{onClick:()=>j(e),disabled:null!==l,variant:l===e?u?"default":"destructive":"outline",className:"w-full h-16 text-2xl font-bold",children:e})},e))}),(0,a.jsx)(aK,{children:null!==u&&(0,a.jsx)(nM.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:`text-center p-4 rounded-lg ${u?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:u?"\uD83C\uDF89 Correct! Great job!":`❌ The answer was ${y.answer}. Keep trying!`})}),(0,a.jsx)(aK,{children:p&&(0,a.jsx)(nM.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 pointer-events-none flex items-center justify-center z-50",children:(0,a.jsx)(nM.div,{animate:{scale:[1,1.5,1],rotate:[0,360,0]},transition:{duration:1},className:"text-6xl",children:"\uD83C\uDF1F"})})})]})}},{id:"shapes",title:"Shape Matching",description:"Identify and match different shapes",icon:(0,a.jsx)(nz,{className:"h-8 w-8"}),grade:"Grade 1",difficulty:"Easy",color:"from-purple-400 to-purple-600",component:function({onComplete:e}){let[t,i]=(0,o.useState)(1),[s,r]=(0,o.useState)(0),[n,l]=(0,o.useState)(aJ[0]),[d,u]=(0,o.useState)(null),[h,c]=(0,o.useState)(null),[m,p]=(0,o.useState)(!1),[f,g]=(0,o.useState)(!1),y=e=>{let t=[e],i=aJ.filter(t=>t.id!==e.id);for(;t.length<4&&i.length>0;){let e=Math.floor(Math.random()*i.length),s=i.splice(e,1)[0];t.push(s)}return t.sort(()=>Math.random()-.5)},[x,v]=(0,o.useState)(y(n)),b=i=>{u(i);let a=i.id===n.id;c(a),a&&(r(s+15),g(!0),setTimeout(()=>g(!1),1e3)),setTimeout(()=>{t>=8?(p(!0),e?.(s+15*!!a)):w()},1500)},w=()=>{let e=aJ[Math.floor(Math.random()*aJ.length)];i(t+1),l(e),v(y(e)),u(null),c(null)};return m?(0,a.jsxs)(aW,{className:"p-8 text-center max-w-md mx-auto",children:[(0,a.jsxs)(nM.div,{initial:{scale:0},animate:{scale:1},className:"mb-6",children:[(0,a.jsx)(nW,{className:"h-16 w-16 text-yellow-500 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Shape Master!"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"You've mastered shape recognition!"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-100 to-pink-100 p-4 rounded-lg mb-6",children:(0,a.jsxs)("p",{className:"text-lg font-semibold text-purple-700",children:["Final Score: ",s," points"]})})]}),(0,a.jsxs)(aU,{onClick:()=>{i(1),r(0),u(null),c(null),p(!1);let e=aJ[Math.floor(Math.random()*aJ.length)];l(e),v(y(e))},className:"w-full",children:[(0,a.jsx)(aZ,{className:"mr-2 h-4 w-4"}),"Play Again"]})]}):(0,a.jsxs)(aW,{className:"p-6 max-w-2xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n$,{className:"h-6 w-6 text-yellow-500"}),(0,a.jsxs)("span",{className:"text-lg font-semibold",children:["Score: ",s]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Round ",t," of 8"]})]}),(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,a.jsx)(nz,{className:"h-8 w-8 text-purple-600 mr-2"}),(0,a.jsxs)("h2",{className:"text-2xl font-bold text-gray-800",children:["Find the ",n.name,"!"]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-lg mb-6",children:[(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-4",children:"Click on the shape that matches:"}),(0,a.jsx)(nM.div,{initial:{scale:0},animate:{scale:1},className:"flex justify-center",children:(0,a.jsx)("svg",{width:"120",height:"120",className:"drop-shadow-lg",children:(0,a.jsx)("g",{fill:n.color,stroke:"#333",strokeWidth:"2",dangerouslySetInnerHTML:{__html:n.svg}})})}),(0,a.jsx)("p",{className:"text-xl font-bold text-purple-700 mt-4",children:n.name})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:x.map((e,t)=>(0,a.jsx)(nM.div,{initial:{scale:0,rotate:-180},animate:{scale:1,rotate:0},transition:{delay:.1*t},whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,a.jsxs)(aU,{onClick:()=>b(e),disabled:null!==d,variant:d?.id===e.id?h?"default":"destructive":"outline",className:"w-full h-32 p-4 flex flex-col items-center justify-center",children:[(0,a.jsx)("svg",{width:"60",height:"60",className:"mb-2",children:(0,a.jsx)("g",{fill:e.color,stroke:"#333",strokeWidth:"1",dangerouslySetInnerHTML:{__html:e.svg}})}),(0,a.jsx)("span",{className:"text-sm font-medium",children:e.name})]})},e.id))}),(0,a.jsx)(aK,{children:null!==h&&(0,a.jsx)(nM.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:`text-center p-4 rounded-lg ${h?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:h?"\uD83C\uDF89 Perfect! You found the right shape!":`❌ That was a ${d?.name}. Look for the ${n.name}!`})}),(0,a.jsx)(aK,{children:f&&(0,a.jsx)(nM.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 pointer-events-none flex items-center justify-center z-50",children:(0,a.jsx)(nM.div,{animate:{scale:[1,1.3,1],rotate:[0,180,360]},transition:{duration:1},className:"text-6xl",children:"\uD83C\uDFA8"})})})]})}},{id:"sequences",title:"Number Patterns",description:"Complete number sequences and patterns",icon:(0,a.jsx)(nO,{className:"h-8 w-8"}),grade:"Grade 2-3",difficulty:"Medium",color:"from-indigo-400 to-indigo-600",component:function({maxNumber:e=20,onComplete:t}){let[i,s]=(0,o.useState)(1),[r,n]=(0,o.useState)(0),[l,d]=(0,o.useState)(null),[u,h]=(0,o.useState)(null),[c,m]=(0,o.useState)(!1),[p,f]=(0,o.useState)(!1),g=()=>{let t=["counting","skip2","skip5","skip10"],i=t[Math.floor(Math.random()*t.length)],s=[],r=1,n=1;switch(i){case"counting":r=1,n=Math.floor(Math.random()*(e-6))+1;break;case"skip2":r=2,n=2*Math.floor(5*Math.random())+2;break;case"skip5":r=5,n=5;break;case"skip10":r=10,n=10}for(let e=0;e<5;e++)s.push(n+e*r);let a=Math.floor(3*Math.random())+1,o=s[a];return{sequence:s.map((e,t)=>t===a?null:e),missingNumber:o,missingIndex:a,step:r,type:i}},[y,x]=(0,o.useState)(g()),v=t=>{let i=[t];for(;i.length<4;){let s=t+(Math.floor(6*Math.random())-3);s>0&&s<=e&&!i.includes(s)&&i.push(s)}return i.sort(()=>Math.random()-.5)},[b,w]=(0,o.useState)(v(y.missingNumber)),j=e=>{d(e);let s=e===y.missingNumber;h(s),s&&(n(r+20),f(!0),setTimeout(()=>f(!1),1e3)),setTimeout(()=>{i>=8?(m(!0),t?.(r+20*!!s)):k()},1500)},k=()=>{let e=g();s(i+1),x(e),w(v(e.missingNumber)),d(null),h(null)};return c?(0,a.jsxs)(aW,{className:"p-8 text-center max-w-md mx-auto",children:[(0,a.jsxs)(nM.div,{initial:{scale:0},animate:{scale:1},className:"mb-6",children:[(0,a.jsx)(nW,{className:"h-16 w-16 text-yellow-500 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Pattern Master!"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"You've mastered number patterns!"}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-100 to-purple-100 p-4 rounded-lg mb-6",children:(0,a.jsxs)("p",{className:"text-lg font-semibold text-blue-700",children:["Final Score: ",r," points"]})})]}),(0,a.jsxs)(aU,{onClick:()=>{s(1),n(0),d(null),h(null),m(!1);let e=g();x(e),w(v(e.missingNumber))},className:"w-full",children:[(0,a.jsx)(aZ,{className:"mr-2 h-4 w-4"}),"Play Again"]})]}):(0,a.jsxs)(aW,{className:"p-6 max-w-3xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n$,{className:"h-6 w-6 text-yellow-500"}),(0,a.jsxs)("span",{className:"text-lg font-semibold",children:["Score: ",r]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Problem ",i," of 8"]})]}),(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Complete the Number Pattern"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-4",children:((e,t)=>{switch(e){case"counting":return"Counting by 1s";case"skip2":return"Counting by 2s";case"skip5":return"Counting by 5s";case"skip10":return"Counting by 10s";default:return`Counting by ${t}s`}})(y.type,y.step)}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg mb-6",children:(0,a.jsx)("div",{className:"flex items-center justify-center space-x-4",children:y.sequence.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(nM.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2*t},className:`w-16 h-16 rounded-lg flex items-center justify-center text-2xl font-bold ${null===e?"bg-yellow-200 border-2 border-dashed border-yellow-400 text-yellow-600":"bg-white border-2 border-blue-300 text-blue-700"}`,children:null===e?"?":e}),t<y.sequence.length-1&&(0,a.jsx)(nO,{className:"h-6 w-6 text-gray-400 mx-2"})]},t))})})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:b.map(e=>(0,a.jsx)(nM.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,a.jsx)(aU,{onClick:()=>j(e),disabled:null!==l,variant:l===e?u?"default":"destructive":"outline",className:"w-full h-16 text-2xl font-bold",children:e})},e))}),(0,a.jsx)(aK,{children:null!==u&&(0,a.jsx)(nM.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:`text-center p-4 rounded-lg ${u?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:u?"\uD83C\uDF89 Excellent! You found the pattern!":`❌ The missing number was ${y.missingNumber}. Keep practicing patterns!`})}),(0,a.jsx)(aK,{children:p&&(0,a.jsx)(nM.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 pointer-events-none flex items-center justify-center z-50",children:(0,a.jsx)(nM.div,{animate:{scale:[1,1.4,1],rotate:[0,360,0]},transition:{duration:1},className:"text-6xl",children:"\uD83E\uDDE9"})})})]})}},{id:"multiplication",title:"Times Tables",description:"Master multiplication with visual aids",icon:(0,a.jsx)(nI,{className:"h-8 w-8"}),grade:"Grade 3-4",difficulty:"Hard",color:"from-orange-400 to-orange-600",component:function({maxTable:e=10,onComplete:t}){let[i,s]=(0,o.useState)(1),[r,n]=(0,o.useState)(0),[l,d]=(0,o.useState)(null),[u,h]=(0,o.useState)(null),[c,m]=(0,o.useState)(!1),[p,f]=(0,o.useState)(!1),[g,y]=(0,o.useState)(0),x=()=>{let t=Math.floor(Math.random()*e)+1,i=Math.floor(Math.random()*e)+1;return{num1:t,num2:i,answer:t*i}},[v,b]=(0,o.useState)(x()),w=e=>{let t=[e];for(;t.length<4;){let i=[e+v.num1,e+v.num2,e-v.num1,e-v.num2,(v.num1+1)*v.num2,v.num1*(v.num2+1),(v.num1-1)*v.num2,v.num1*(v.num2-1)],s=i[Math.floor(Math.random()*i.length)];s>0&&s!==e&&!t.includes(s)&&t.push(s)}return t.sort(()=>Math.random()-.5)},[j,k]=(0,o.useState)(w(v.answer)),P=e=>{d(e);let s=e===v.answer;h(s),s?(n(r+(15+5*g)),y(g+1),f(!0),setTimeout(()=>f(!1),1e3)):y(0),setTimeout(()=>{i>=12?(m(!0),t?.(r+(s?15+5*g:0))):T()},1500)},T=()=>{let e=x();s(i+1),b(e),k(w(e.answer)),d(null),h(null)};return c?(0,a.jsxs)(aW,{className:"p-8 text-center max-w-md mx-auto",children:[(0,a.jsxs)(nM.div,{initial:{scale:0},animate:{scale:1},className:"mb-6",children:[(0,a.jsx)(nW,{className:"h-16 w-16 text-yellow-500 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Multiplication Master!"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"You've conquered the times tables!"}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-orange-100 to-red-100 p-4 rounded-lg mb-6",children:[(0,a.jsxs)("p",{className:"text-lg font-semibold text-orange-700",children:["Final Score: ",r," points"]}),g>3&&(0,a.jsxs)("p",{className:"text-sm text-orange-600 mt-1",children:["\uD83D\uDD25 Best streak: ",g," in a row!"]})]})]}),(0,a.jsxs)(aU,{onClick:()=>{s(1),n(0),y(0),d(null),h(null),m(!1);let e=x();b(e),k(w(e.answer))},className:"w-full",children:[(0,a.jsx)(aZ,{className:"mr-2 h-4 w-4"}),"Play Again"]})]}):(0,a.jsxs)(aW,{className:"p-6 max-w-2xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n$,{className:"h-6 w-6 text-yellow-500"}),(0,a.jsxs)("span",{className:"text-lg font-semibold",children:["Score: ",r]})]}),g>0&&(0,a.jsx)("div",{className:"flex items-center space-x-1 bg-orange-100 px-3 py-1 rounded-full",children:(0,a.jsxs)("span",{className:"text-orange-600 font-semibold",children:["\uD83D\uDD25 ",g]})})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Problem ",i," of 12"]})]}),(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Solve the Multiplication:"}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-orange-50 to-red-50 p-8 rounded-lg mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4 text-6xl font-bold text-orange-700",children:[(0,a.jsx)(nM.span,{initial:{scale:0},animate:{scale:1},transition:{delay:.2},children:v.num1}),(0,a.jsx)(nM.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.4},children:(0,a.jsx)(nI,{className:"h-12 w-12"})}),(0,a.jsx)(nM.span,{initial:{scale:0},animate:{scale:1},transition:{delay:.6},children:v.num2}),(0,a.jsx)(nM.span,{initial:{scale:0},animate:{scale:1},transition:{delay:.8},children:"="}),(0,a.jsx)(nM.span,{initial:{scale:0},animate:{scale:1},transition:{delay:1},className:"text-gray-400",children:"?"})]}),v.num1<=5&&v.num2<=5&&(0,a.jsxs)(nM.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:1.2},className:"mt-6",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Visual help:"}),(0,a.jsx)("div",{className:"flex flex-col items-center space-y-2",children:Array.from({length:v.num1},(e,t)=>(0,a.jsx)("div",{className:"flex space-x-2",children:Array.from({length:v.num2},(e,t)=>(0,a.jsx)("div",{className:"w-4 h-4 bg-orange-300 rounded-full"},t))},t))})]})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:j.map(e=>(0,a.jsx)(nM.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,a.jsx)(aU,{onClick:()=>P(e),disabled:null!==l,variant:l===e?u?"default":"destructive":"outline",className:"w-full h-16 text-2xl font-bold",children:e})},e))}),(0,a.jsx)(aK,{children:null!==u&&(0,a.jsx)(nM.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:`text-center p-4 rounded-lg ${u?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:u?g>1?`🎉 Correct! Amazing streak of ${g}!`:"\uD83C\uDF89 Correct! Great job!":`❌ The answer was ${v.answer}. Remember: ${v.num1} \xd7 ${v.num2} = ${v.answer}`})}),(0,a.jsx)(aK,{children:p&&(0,a.jsx)(nM.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 pointer-events-none flex items-center justify-center z-50",children:(0,a.jsx)(nM.div,{animate:{scale:[1,1.5,1],rotate:[0,360,0]},transition:{duration:1},className:"text-6xl",children:g>3?"\uD83D\uDD25":"⭐"})})})]})}}];function a1({onGameComplete:e}){let[t,i]=(0,o.useState)(null),[s,r]=(0,o.useState)(0),[n,l]=(0,o.useState)(new Set),d=e=>{i(e)},u=e=>{switch(e){case"Easy":return"bg-green-100 text-green-800";case"Medium":return"bg-yellow-100 text-yellow-800";case"Hard":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};if(t){let n=t.component;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(aU,{variant:"ghost",onClick:()=>{i(null)},children:"← Back to Games"}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:t.title}),(0,a.jsx)("p",{className:"text-gray-600",children:t.description})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n$,{className:"h-5 w-5 text-yellow-500"}),(0,a.jsxs)("span",{className:"font-semibold",children:["Total: ",s]})]})]}),(0,a.jsx)(n,{onComplete:n=>{t&&(r(s+n),l(e=>new Set([...e,t.id])),e?.(t.id,n),i(null))}})]})}return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,a.jsx)(nU,{className:"h-8 w-8 text-purple-600 mr-3"}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:"Math Games"})]}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6",children:"Choose a game to practice your math skills!"}),s>0&&(0,a.jsxs)(nM.div,{initial:{scale:0},animate:{scale:1},className:"bg-gradient-to-r from-yellow-100 to-orange-100 p-4 rounded-lg mb-6 inline-block",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(nW,{className:"h-6 w-6 text-yellow-600"}),(0,a.jsxs)("span",{className:"text-lg font-bold text-yellow-700",children:["Total Score: ",s," points"]})]}),(0,a.jsxs)("p",{className:"text-sm text-yellow-600",children:["Games completed: ",n.size,"/",a0.length]})]})]}),(0,a.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:a0.map((e,t)=>(0,a.jsx)(nM.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,a.jsxs)(aW,{className:"p-6 h-full cursor-pointer hover:shadow-lg transition-all relative overflow-hidden",children:[n.has(e.id)&&(0,a.jsx)("div",{className:"absolute top-2 right-2 bg-green-500 text-white rounded-full p-1",children:(0,a.jsx)(n$,{className:"h-4 w-4 fill-current"})}),(0,a.jsx)("div",{className:`bg-gradient-to-r ${e.color} p-4 rounded-lg mb-4 text-white w-fit`,children:e.icon}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-1",children:e.title}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:e.grade}),(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${u(e.difficulty)}`,children:e.difficulty})]}),(0,a.jsx)(aU,{onClick:()=>d(e),className:"w-full mt-4",variant:n.has(e.id)?"outline":"default",children:n.has(e.id)?"Play Again":"Start Game"})]})]})},e.id))}),n.size>0&&(0,a.jsxs)(nM.div,{initial:{opacity:0},animate:{opacity:1},className:"bg-white p-6 rounded-lg border",children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-800 mb-4",children:"Your Progress"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"Games Completed"}),(0,a.jsxs)("span",{className:"font-semibold",children:[n.size,"/",a0.length]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-500",style:{width:`${n.size/a0.length*100}%`}})}),n.size===a0.length&&(0,a.jsx)(nM.p,{initial:{opacity:0},animate:{opacity:1},className:"text-green-600 font-semibold text-center mt-4",children:"\uD83C\uDF89 Congratulations! You've completed all games!"})]})]})]})}var a2=i(5814),a5=i.n(a2);let a6=nL("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);function a3(){return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)(a5(),{href:"/",children:(0,a.jsxs)(aU,{variant:"ghost",className:"mb-4",children:[(0,a.jsx)(a6,{className:"mr-2 h-4 w-4"}),"Back to Home"]})}),(0,a.jsx)("h1",{className:"text-4xl font-bold text-center mb-4",children:(0,a.jsx)("span",{className:"bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent",children:"MathQuest Interactive Demo"})}),(0,a.jsx)("p",{className:"text-xl text-gray-600 text-center max-w-2xl mx-auto",children:"Try our collection of interactive math games designed for different grade levels. Choose a game and start learning!"})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)(a1,{onGameComplete:(e,t)=>{console.log(`Game ${e} completed with score: ${t}`)}})}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 mt-12",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,a.jsx)("div",{className:"text-3xl mb-3",children:"\uD83C\uDFAF"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Interactive Learning"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Engaging games that make learning math fun and memorable for children."})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,a.jsx)("div",{className:"text-3xl mb-3",children:"\uD83D\uDCCA"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Progress Tracking"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Real-time feedback and detailed progress reports for students and parents."})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,a.jsx)("div",{className:"text-3xl mb-3",children:"\uD83C\uDFC6"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Achievement System"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Badges, points, and rewards that motivate students to keep learning."})]})]}),(0,a.jsxs)("div",{className:"text-center mt-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Ready to Start Learning?"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Join thousands of students already improving their math skills with MathQuest."}),(0,a.jsxs)("div",{className:"space-x-4",children:[(0,a.jsx)(a5(),{href:"/register",children:(0,a.jsx)(aU,{size:"lg",children:"Get Started Free"})}),(0,a.jsx)(a5(),{href:"/",children:(0,a.jsx)(aU,{variant:"outline",size:"lg",children:"Learn More"})})]})]})]})})}},3873:e=>{"use strict";e.exports=require("path")},3920:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>s});let s=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\augument\\\\onstud\\\\mathquest\\\\src\\\\app\\\\demo\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\demo\\page.tsx","default")},4431:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>d,metadata:()=>o,viewport:()=>l});var s=i(7413),r=i(5041),n=i.n(r);i(1135);var a=i(3622);let o={title:"MathQuest - Gamified Mathematics Learning",description:"Interactive mathematics learning platform for grades 1-10 with games, achievements, and personalized progress tracking",keywords:"mathematics, learning, education, games, kids, math, interactive",authors:[{name:"MathQuest Team"}]},l={width:"device-width",initialScale:1};function d({children:e}){return(0,s.jsx)("html",{lang:"en",className:"h-full",children:(0,s.jsx)("body",{className:`${n().className} h-full bg-gradient-to-br from-blue-50 to-purple-50 antialiased`,children:(0,s.jsx)(a.Providers,{children:e})})})}},4761:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6346,23)),Promise.resolve().then(i.t.bind(i,5543,23)),Promise.resolve().then(i.t.bind(i,5656,23)),Promise.resolve().then(i.t.bind(i,99,23)),Promise.resolve().then(i.t.bind(i,8243,23)),Promise.resolve().then(i.t.bind(i,8827,23)),Promise.resolve().then(i.t.bind(i,2763,23)),Promise.resolve().then(i.t.bind(i,7173,23))},7552:(e,t,i)=>{"use strict";i.d(t,{Providers:()=>n});var s=i(687),r=i(2136);function n({children:e}){return(0,s.jsx)(r.SessionProvider,{children:e})}},7629:(e,t,i)=>{Promise.resolve().then(i.bind(i,3710))},7785:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6444,23)),Promise.resolve().then(i.t.bind(i,6042,23)),Promise.resolve().then(i.t.bind(i,8170,23)),Promise.resolve().then(i.t.bind(i,9477,23)),Promise.resolve().then(i.t.bind(i,9345,23)),Promise.resolve().then(i.t.bind(i,2089,23)),Promise.resolve().then(i.t.bind(i,6577,23)),Promise.resolve().then(i.t.bind(i,1307,23))},7950:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>a.a,__next_app__:()=>h,pages:()=>u,routeModule:()=>c,tree:()=>d});var s=i(5239),r=i(8088),n=i(8170),a=i.n(n),o=i(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);i.d(t,l);let d={children:["",{children:["demo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,3920)),"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\demo\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,4431)),"C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\augument\\onstud\\mathquest\\src\\app\\demo\\page.tsx"],h={require:i,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/demo/page",pathname:"/demo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),s=t.X(0,[447,70,567],()=>i(7950));module.exports=s})();