// MongoDB initialization script
db = db.getSiblingDB('mathquest');

// Create collections
db.createCollection('users');
db.createCollection('progress');
db.createCollection('achievements');

// Create indexes for better performance
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ role: 1, grade: 1 });
db.progress.createIndex({ userId: 1, grade: 1, week: 1 });
db.progress.createIndex({ userId: 1, completed: 1 });
db.achievements.createIndex({ category: 1, rarity: 1 });

// Insert sample achievements
db.achievements.insertMany([
  {
    id: 'first_lesson',
    name: 'First Steps',
    description: 'Complete your first lesson',
    icon: '🎯',
    category: 'completion',
    points: 10,
    requirements: { type: 'lessons_completed', value: 1 },
    rarity: 'common'
  },
  {
    id: 'week_warrior',
    name: 'Week Warrior',
    description: 'Complete all lessons in a week',
    icon: '⚔️',
    category: 'completion',
    points: 50,
    requirements: { type: 'week_completed', value: 1 },
    rarity: 'rare'
  },
  {
    id: 'perfect_score',
    name: 'Perfect Score',
    description: 'Get 100% on any lesson',
    icon: '💯',
    category: 'score',
    points: 25,
    requirements: { type: 'perfect_score', value: 100 },
    rarity: 'rare'
  },
  {
    id: 'speed_demon',
    name: 'Speed Demon',
    description: 'Complete a lesson in under 2 minutes',
    icon: '⚡',
    category: 'time',
    points: 30,
    requirements: { type: 'time_under', value: 120 },
    rarity: 'epic'
  },
  {
    id: 'math_master',
    name: 'Math Master',
    description: 'Complete an entire grade curriculum',
    icon: '👑',
    category: 'completion',
    points: 500,
    requirements: { type: 'grade_completed', value: 1 },
    rarity: 'legendary'
  }
]);

print('Database initialized successfully!');
