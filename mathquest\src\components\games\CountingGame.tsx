'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Star, Trophy, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';

interface CountingGameProps {
  maxNumber?: number;
  onComplete?: (score: number) => void;
}

export function CountingGame({ maxNumber = 10, onComplete }: CountingGameProps) {
  const [currentNumber, setCurrentNumber] = useState(1);
  const [score, setScore] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null);
  const [gameComplete, setGameComplete] = useState(false);
  const [showCelebration, setShowCelebration] = useState(false);

  // Generate random objects to count
  const generateObjects = (count: number) => {
    const objects = ['🍎', '🌟', '🎈', '🐱', '🚗', '🌸', '🎁', '🦋'];
    const selectedObject = objects[Math.floor(Math.random() * objects.length)];
    return Array(count).fill(selectedObject);
  };

  const [objects, setObjects] = useState(generateObjects(currentNumber));

  // Generate answer options
  const generateOptions = (correct: number) => {
    const options = [correct];
    while (options.length < 4) {
      const option = Math.floor(Math.random() * maxNumber) + 1;
      if (!options.includes(option)) {
        options.push(option);
      }
    }
    return options.sort(() => Math.random() - 0.5);
  };

  const [options, setOptions] = useState(generateOptions(currentNumber));

  const handleAnswer = (answer: number) => {
    setSelectedAnswer(answer);
    const correct = answer === currentNumber;
    setIsCorrect(correct);

    if (correct) {
      setScore(score + 10);
      setShowCelebration(true);
      setTimeout(() => setShowCelebration(false), 1000);
    }

    setTimeout(() => {
      if (currentNumber >= maxNumber) {
        setGameComplete(true);
        onComplete?.(score + (correct ? 10 : 0));
      } else {
        nextQuestion();
      }
    }, 1500);
  };

  const nextQuestion = () => {
    const nextNum = currentNumber + 1;
    setCurrentNumber(nextNum);
    setObjects(generateObjects(nextNum));
    setOptions(generateOptions(nextNum));
    setSelectedAnswer(null);
    setIsCorrect(null);
  };

  const resetGame = () => {
    setCurrentNumber(1);
    setScore(0);
    setSelectedAnswer(null);
    setIsCorrect(null);
    setGameComplete(false);
    setObjects(generateObjects(1));
    setOptions(generateOptions(1));
  };

  if (gameComplete) {
    return (
      <Card className="p-8 text-center max-w-md mx-auto">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="mb-6"
        >
          <Trophy className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-800 mb-2">
            Congratulations!
          </h2>
          <p className="text-gray-600 mb-4">
            You completed the counting game!
          </p>
          <div className="bg-gradient-to-r from-purple-100 to-blue-100 p-4 rounded-lg mb-6">
            <p className="text-lg font-semibold text-purple-700">
              Final Score: {score} points
            </p>
          </div>
        </motion.div>
        <Button onClick={resetGame} className="w-full">
          <RotateCcw className="mr-2 h-4 w-4" />
          Play Again
        </Button>
      </Card>
    );
  }

  return (
    <Card className="p-6 max-w-2xl mx-auto">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-2">
          <Star className="h-6 w-6 text-yellow-500" />
          <span className="text-lg font-semibold">Score: {score}</span>
        </div>
        <div className="text-sm text-gray-600">
          Question {currentNumber} of {maxNumber}
        </div>
      </div>

      {/* Question */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">
          Count the objects below:
        </h2>
        
        {/* Objects to count */}
        <div className="grid grid-cols-5 gap-4 justify-items-center mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
          <AnimatePresence>
            {objects.map((obj, index) => (
              <motion.div
                key={index}
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ delay: index * 0.1 }}
                className="text-4xl"
              >
                {obj}
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      </div>

      {/* Answer Options */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        {options.map((option) => (
          <motion.div key={option} whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button
              onClick={() => handleAnswer(option)}
              disabled={selectedAnswer !== null}
              variant={
                selectedAnswer === option
                  ? isCorrect
                    ? 'default'
                    : 'destructive'
                  : 'outline'
              }
              className="w-full h-16 text-2xl font-bold"
            >
              {option}
            </Button>
          </motion.div>
        ))}
      </div>

      {/* Feedback */}
      <AnimatePresence>
        {isCorrect !== null && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className={`text-center p-4 rounded-lg ${
              isCorrect
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}
          >
            {isCorrect ? '🎉 Correct! Well done!' : '❌ Try again next time!'}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Celebration Animation */}
      <AnimatePresence>
        {showCelebration && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 pointer-events-none flex items-center justify-center z-50"
          >
            <motion.div
              animate={{ 
                scale: [1, 1.2, 1],
                rotate: [0, 360, 0]
              }}
              transition={{ duration: 1 }}
              className="text-6xl"
            >
              ⭐
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  );
}
