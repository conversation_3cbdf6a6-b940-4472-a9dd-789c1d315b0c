import mongoose, { Document, Schema } from 'mongoose';

export interface IAchievement extends Document {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'completion' | 'streak' | 'score' | 'time' | 'special';
  points: number;
  requirements: {
    type: string;
    value: number;
    grade?: number;
    week?: number;
  };
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  createdAt: Date;
}

const AchievementSchema: Schema = new Schema({
  id: {
    type: String,
    required: true,
    unique: true
  },
  name: {
    type: String,
    required: true,
    maxlength: 50
  },
  description: {
    type: String,
    required: true,
    maxlength: 200
  },
  icon: {
    type: String,
    required: true
  },
  category: {
    type: String,
    enum: ['completion', 'streak', 'score', 'time', 'special'],
    required: true
  },
  points: {
    type: Number,
    required: true,
    min: 0
  },
  requirements: {
    type: {
      type: String,
      required: true
    },
    value: {
      type: Number,
      required: true
    },
    grade: Number,
    week: Number
  },
  rarity: {
    type: String,
    enum: ['common', 'rare', 'epic', 'legendary'],
    default: 'common'
  }
}, {
  timestamps: true
});

AchievementSchema.index({ category: 1, rarity: 1 });

export default mongoose.models.Achievement || mongoose.model<IAchievement>('Achievement', AchievementSchema);
